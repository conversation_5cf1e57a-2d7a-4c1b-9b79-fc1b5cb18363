(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const d of u)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function o(u){const d={};return u.integrity&&(d.integrity=u.integrity),u.referrerPolicy&&(d.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?d.credentials="include":u.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(u){if(u.ep)return;u.ep=!0;const d=o(u);fetch(u.href,d)}})();function Ty(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var na={exports:{}},fi={},ra={exports:{}},ae={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hd;function Ey(){if(hd)return ae;hd=1;var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),x=Symbol.iterator;function S(C){return C===null||typeof C!="object"?null:(C=x&&C[x]||C["@@iterator"],typeof C=="function"?C:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},N=Object.assign,M={};function R(C,V,se){this.props=C,this.context=V,this.refs=M,this.updater=se||A}R.prototype.isReactComponent={},R.prototype.setState=function(C,V){if(typeof C!="object"&&typeof C!="function"&&C!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,C,V,"setState")},R.prototype.forceUpdate=function(C){this.updater.enqueueForceUpdate(this,C,"forceUpdate")};function _(){}_.prototype=R.prototype;function F(C,V,se){this.props=C,this.context=V,this.refs=M,this.updater=se||A}var U=F.prototype=new _;U.constructor=F,N(U,R.prototype),U.isPureReactComponent=!0;var q=Array.isArray,G=Object.prototype.hasOwnProperty,W={current:null},ne={key:!0,ref:!0,__self:!0,__source:!0};function X(C,V,se){var ue,fe={},de=null,ve=null;if(V!=null)for(ue in V.ref!==void 0&&(ve=V.ref),V.key!==void 0&&(de=""+V.key),V)G.call(V,ue)&&!ne.hasOwnProperty(ue)&&(fe[ue]=V[ue]);var he=arguments.length-2;if(he===1)fe.children=se;else if(1<he){for(var Pe=Array(he),at=0;at<he;at++)Pe[at]=arguments[at+2];fe.children=Pe}if(C&&C.defaultProps)for(ue in he=C.defaultProps,he)fe[ue]===void 0&&(fe[ue]=he[ue]);return{$$typeof:n,type:C,key:de,ref:ve,props:fe,_owner:W.current}}function ge(C,V){return{$$typeof:n,type:C.type,key:V,ref:C.ref,props:C.props,_owner:C._owner}}function ye(C){return typeof C=="object"&&C!==null&&C.$$typeof===n}function Fe(C){var V={"=":"=0",":":"=2"};return"$"+C.replace(/[=:]/g,function(se){return V[se]})}var le=/\/+/g;function be(C,V){return typeof C=="object"&&C!==null&&C.key!=null?Fe(""+C.key):V.toString(36)}function Ie(C,V,se,ue,fe){var de=typeof C;(de==="undefined"||de==="boolean")&&(C=null);var ve=!1;if(C===null)ve=!0;else switch(de){case"string":case"number":ve=!0;break;case"object":switch(C.$$typeof){case n:case r:ve=!0}}if(ve)return ve=C,fe=fe(ve),C=ue===""?"."+be(ve,0):ue,q(fe)?(se="",C!=null&&(se=C.replace(le,"$&/")+"/"),Ie(fe,V,se,"",function(at){return at})):fe!=null&&(ye(fe)&&(fe=ge(fe,se+(!fe.key||ve&&ve.key===fe.key?"":(""+fe.key).replace(le,"$&/")+"/")+C)),V.push(fe)),1;if(ve=0,ue=ue===""?".":ue+":",q(C))for(var he=0;he<C.length;he++){de=C[he];var Pe=ue+be(de,he);ve+=Ie(de,V,se,Pe,fe)}else if(Pe=S(C),typeof Pe=="function")for(C=Pe.call(C),he=0;!(de=C.next()).done;)de=de.value,Pe=ue+be(de,he++),ve+=Ie(de,V,se,Pe,fe);else if(de==="object")throw V=String(C),Error("Objects are not valid as a React child (found: "+(V==="[object Object]"?"object with keys {"+Object.keys(C).join(", ")+"}":V)+"). If you meant to render a collection of children, use an array instead.");return ve}function tt(C,V,se){if(C==null)return C;var ue=[],fe=0;return Ie(C,ue,"","",function(de){return V.call(se,de,fe++)}),ue}function _e(C){if(C._status===-1){var V=C._result;V=V(),V.then(function(se){(C._status===0||C._status===-1)&&(C._status=1,C._result=se)},function(se){(C._status===0||C._status===-1)&&(C._status=2,C._result=se)}),C._status===-1&&(C._status=0,C._result=V)}if(C._status===1)return C._result.default;throw C._result}var ie={current:null},z={transition:null},Z={ReactCurrentDispatcher:ie,ReactCurrentBatchConfig:z,ReactCurrentOwner:W};function B(){throw Error("act(...) is not supported in production builds of React.")}return ae.Children={map:tt,forEach:function(C,V,se){tt(C,function(){V.apply(this,arguments)},se)},count:function(C){var V=0;return tt(C,function(){V++}),V},toArray:function(C){return tt(C,function(V){return V})||[]},only:function(C){if(!ye(C))throw Error("React.Children.only expected to receive a single React element child.");return C}},ae.Component=R,ae.Fragment=o,ae.Profiler=u,ae.PureComponent=F,ae.StrictMode=l,ae.Suspense=m,ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Z,ae.act=B,ae.cloneElement=function(C,V,se){if(C==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+C+".");var ue=N({},C.props),fe=C.key,de=C.ref,ve=C._owner;if(V!=null){if(V.ref!==void 0&&(de=V.ref,ve=W.current),V.key!==void 0&&(fe=""+V.key),C.type&&C.type.defaultProps)var he=C.type.defaultProps;for(Pe in V)G.call(V,Pe)&&!ne.hasOwnProperty(Pe)&&(ue[Pe]=V[Pe]===void 0&&he!==void 0?he[Pe]:V[Pe])}var Pe=arguments.length-2;if(Pe===1)ue.children=se;else if(1<Pe){he=Array(Pe);for(var at=0;at<Pe;at++)he[at]=arguments[at+2];ue.children=he}return{$$typeof:n,type:C.type,key:fe,ref:de,props:ue,_owner:ve}},ae.createContext=function(C){return C={$$typeof:f,_currentValue:C,_currentValue2:C,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},C.Provider={$$typeof:d,_context:C},C.Consumer=C},ae.createElement=X,ae.createFactory=function(C){var V=X.bind(null,C);return V.type=C,V},ae.createRef=function(){return{current:null}},ae.forwardRef=function(C){return{$$typeof:p,render:C}},ae.isValidElement=ye,ae.lazy=function(C){return{$$typeof:y,_payload:{_status:-1,_result:C},_init:_e}},ae.memo=function(C,V){return{$$typeof:g,type:C,compare:V===void 0?null:V}},ae.startTransition=function(C){var V=z.transition;z.transition={};try{C()}finally{z.transition=V}},ae.unstable_act=B,ae.useCallback=function(C,V){return ie.current.useCallback(C,V)},ae.useContext=function(C){return ie.current.useContext(C)},ae.useDebugValue=function(){},ae.useDeferredValue=function(C){return ie.current.useDeferredValue(C)},ae.useEffect=function(C,V){return ie.current.useEffect(C,V)},ae.useId=function(){return ie.current.useId()},ae.useImperativeHandle=function(C,V,se){return ie.current.useImperativeHandle(C,V,se)},ae.useInsertionEffect=function(C,V){return ie.current.useInsertionEffect(C,V)},ae.useLayoutEffect=function(C,V){return ie.current.useLayoutEffect(C,V)},ae.useMemo=function(C,V){return ie.current.useMemo(C,V)},ae.useReducer=function(C,V,se){return ie.current.useReducer(C,V,se)},ae.useRef=function(C){return ie.current.useRef(C)},ae.useState=function(C){return ie.current.useState(C)},ae.useSyncExternalStore=function(C,V,se){return ie.current.useSyncExternalStore(C,V,se)},ae.useTransition=function(){return ie.current.useTransition()},ae.version="18.3.1",ae}var md;function Fa(){return md||(md=1,ra.exports=Ey()),ra.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gd;function Ay(){if(gd)return fi;gd=1;var n=Fa(),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,u=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(p,m,g){var y,x={},S=null,A=null;g!==void 0&&(S=""+g),m.key!==void 0&&(S=""+m.key),m.ref!==void 0&&(A=m.ref);for(y in m)l.call(m,y)&&!d.hasOwnProperty(y)&&(x[y]=m[y]);if(p&&p.defaultProps)for(y in m=p.defaultProps,m)x[y]===void 0&&(x[y]=m[y]);return{$$typeof:r,type:p,key:S,ref:A,props:x,_owner:u.current}}return fi.Fragment=o,fi.jsx=f,fi.jsxs=f,fi}var yd;function My(){return yd||(yd=1,na.exports=Ay()),na.exports}var I=My(),b=Fa();const Ry=Ty(b);var Uo={},ia={exports:{}},lt={},oa={exports:{}},sa={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vd;function Ny(){return vd||(vd=1,function(n){function r(z,Z){var B=z.length;z.push(Z);e:for(;0<B;){var C=B-1>>>1,V=z[C];if(0<u(V,Z))z[C]=Z,z[B]=V,B=C;else break e}}function o(z){return z.length===0?null:z[0]}function l(z){if(z.length===0)return null;var Z=z[0],B=z.pop();if(B!==Z){z[0]=B;e:for(var C=0,V=z.length,se=V>>>1;C<se;){var ue=2*(C+1)-1,fe=z[ue],de=ue+1,ve=z[de];if(0>u(fe,B))de<V&&0>u(ve,fe)?(z[C]=ve,z[de]=B,C=de):(z[C]=fe,z[ue]=B,C=ue);else if(de<V&&0>u(ve,B))z[C]=ve,z[de]=B,C=de;else break e}}return Z}function u(z,Z){var B=z.sortIndex-Z.sortIndex;return B!==0?B:z.id-Z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var f=Date,p=f.now();n.unstable_now=function(){return f.now()-p}}var m=[],g=[],y=1,x=null,S=3,A=!1,N=!1,M=!1,R=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,F=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function U(z){for(var Z=o(g);Z!==null;){if(Z.callback===null)l(g);else if(Z.startTime<=z)l(g),Z.sortIndex=Z.expirationTime,r(m,Z);else break;Z=o(g)}}function q(z){if(M=!1,U(z),!N)if(o(m)!==null)N=!0,_e(G);else{var Z=o(g);Z!==null&&ie(q,Z.startTime-z)}}function G(z,Z){N=!1,M&&(M=!1,_(X),X=-1),A=!0;var B=S;try{for(U(Z),x=o(m);x!==null&&(!(x.expirationTime>Z)||z&&!Fe());){var C=x.callback;if(typeof C=="function"){x.callback=null,S=x.priorityLevel;var V=C(x.expirationTime<=Z);Z=n.unstable_now(),typeof V=="function"?x.callback=V:x===o(m)&&l(m),U(Z)}else l(m);x=o(m)}if(x!==null)var se=!0;else{var ue=o(g);ue!==null&&ie(q,ue.startTime-Z),se=!1}return se}finally{x=null,S=B,A=!1}}var W=!1,ne=null,X=-1,ge=5,ye=-1;function Fe(){return!(n.unstable_now()-ye<ge)}function le(){if(ne!==null){var z=n.unstable_now();ye=z;var Z=!0;try{Z=ne(!0,z)}finally{Z?be():(W=!1,ne=null)}}else W=!1}var be;if(typeof F=="function")be=function(){F(le)};else if(typeof MessageChannel<"u"){var Ie=new MessageChannel,tt=Ie.port2;Ie.port1.onmessage=le,be=function(){tt.postMessage(null)}}else be=function(){R(le,0)};function _e(z){ne=z,W||(W=!0,be())}function ie(z,Z){X=R(function(){z(n.unstable_now())},Z)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(z){z.callback=null},n.unstable_continueExecution=function(){N||A||(N=!0,_e(G))},n.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ge=0<z?Math.floor(1e3/z):5},n.unstable_getCurrentPriorityLevel=function(){return S},n.unstable_getFirstCallbackNode=function(){return o(m)},n.unstable_next=function(z){switch(S){case 1:case 2:case 3:var Z=3;break;default:Z=S}var B=S;S=Z;try{return z()}finally{S=B}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(z,Z){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var B=S;S=z;try{return Z()}finally{S=B}},n.unstable_scheduleCallback=function(z,Z,B){var C=n.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?C+B:C):B=C,z){case 1:var V=-1;break;case 2:V=250;break;case 5:V=**********;break;case 4:V=1e4;break;default:V=5e3}return V=B+V,z={id:y++,callback:Z,priorityLevel:z,startTime:B,expirationTime:V,sortIndex:-1},B>C?(z.sortIndex=B,r(g,z),o(m)===null&&z===o(g)&&(M?(_(X),X=-1):M=!0,ie(q,B-C))):(z.sortIndex=V,r(m,z),N||A||(N=!0,_e(G))),z},n.unstable_shouldYield=Fe,n.unstable_wrapCallback=function(z){var Z=S;return function(){var B=S;S=Z;try{return z.apply(this,arguments)}finally{S=B}}}}(sa)),sa}var xd;function Dy(){return xd||(xd=1,oa.exports=Ny()),oa.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wd;function Vy(){if(wd)return lt;wd=1;var n=Fa(),r=Dy();function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,i=1;i<arguments.length;i++)t+="&args[]="+encodeURIComponent(arguments[i]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,u={};function d(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(u[e]=t,e=0;e<t.length;e++)l.add(t[e])}var p=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,y={},x={};function S(e){return m.call(x,e)?!0:m.call(y,e)?!1:g.test(e)?x[e]=!0:(y[e]=!0,!1)}function A(e,t,i,s){if(i!==null&&i.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:i!==null?!i.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function N(e,t,i,s){if(t===null||typeof t>"u"||A(e,t,i,s))return!0;if(s)return!1;if(i!==null)switch(i.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function M(e,t,i,s,a,c,h){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=a,this.mustUseProperty=i,this.propertyName=e,this.type=t,this.sanitizeURL=c,this.removeEmptyString=h}var R={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){R[e]=new M(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];R[t]=new M(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){R[e]=new M(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){R[e]=new M(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){R[e]=new M(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){R[e]=new M(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){R[e]=new M(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){R[e]=new M(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){R[e]=new M(e,5,!1,e.toLowerCase(),null,!1,!1)});var _=/[\-:]([a-z])/g;function F(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_,F);R[t]=new M(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_,F);R[t]=new M(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_,F);R[t]=new M(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){R[e]=new M(e,1,!1,e.toLowerCase(),null,!1,!1)}),R.xlinkHref=new M("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){R[e]=new M(e,1,!1,e.toLowerCase(),null,!0,!0)});function U(e,t,i,s){var a=R.hasOwnProperty(t)?R[t]:null;(a!==null?a.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(N(t,i,a,s)&&(i=null),s||a===null?S(t)&&(i===null?e.removeAttribute(t):e.setAttribute(t,""+i)):a.mustUseProperty?e[a.propertyName]=i===null?a.type===3?!1:"":i:(t=a.attributeName,s=a.attributeNamespace,i===null?e.removeAttribute(t):(a=a.type,i=a===3||a===4&&i===!0?"":""+i,s?e.setAttributeNS(s,t,i):e.setAttribute(t,i))))}var q=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,G=Symbol.for("react.element"),W=Symbol.for("react.portal"),ne=Symbol.for("react.fragment"),X=Symbol.for("react.strict_mode"),ge=Symbol.for("react.profiler"),ye=Symbol.for("react.provider"),Fe=Symbol.for("react.context"),le=Symbol.for("react.forward_ref"),be=Symbol.for("react.suspense"),Ie=Symbol.for("react.suspense_list"),tt=Symbol.for("react.memo"),_e=Symbol.for("react.lazy"),ie=Symbol.for("react.offscreen"),z=Symbol.iterator;function Z(e){return e===null||typeof e!="object"?null:(e=z&&e[z]||e["@@iterator"],typeof e=="function"?e:null)}var B=Object.assign,C;function V(e){if(C===void 0)try{throw Error()}catch(i){var t=i.stack.trim().match(/\n( *(at )?)/);C=t&&t[1]||""}return`
`+C+e}var se=!1;function ue(e,t){if(!e||se)return"";se=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(E){var s=E}Reflect.construct(e,[],t)}else{try{t.call()}catch(E){s=E}e.call(t.prototype)}else{try{throw Error()}catch(E){s=E}e()}}catch(E){if(E&&s&&typeof E.stack=="string"){for(var a=E.stack.split(`
`),c=s.stack.split(`
`),h=a.length-1,v=c.length-1;1<=h&&0<=v&&a[h]!==c[v];)v--;for(;1<=h&&0<=v;h--,v--)if(a[h]!==c[v]){if(h!==1||v!==1)do if(h--,v--,0>v||a[h]!==c[v]){var w=`
`+a[h].replace(" at new "," at ");return e.displayName&&w.includes("<anonymous>")&&(w=w.replace("<anonymous>",e.displayName)),w}while(1<=h&&0<=v);break}}}finally{se=!1,Error.prepareStackTrace=i}return(e=e?e.displayName||e.name:"")?V(e):""}function fe(e){switch(e.tag){case 5:return V(e.type);case 16:return V("Lazy");case 13:return V("Suspense");case 19:return V("SuspenseList");case 0:case 2:case 15:return e=ue(e.type,!1),e;case 11:return e=ue(e.type.render,!1),e;case 1:return e=ue(e.type,!0),e;default:return""}}function de(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ne:return"Fragment";case W:return"Portal";case ge:return"Profiler";case X:return"StrictMode";case be:return"Suspense";case Ie:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Fe:return(e.displayName||"Context")+".Consumer";case ye:return(e._context.displayName||"Context")+".Provider";case le:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case tt:return t=e.displayName||null,t!==null?t:de(e.type)||"Memo";case _e:t=e._payload,e=e._init;try{return de(e(t))}catch{}}return null}function ve(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return de(t);case 8:return t===X?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function he(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pe(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function at(e){var t=Pe(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var a=i.get,c=i.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(h){s=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:i.enumerable}),{getValue:function(){return s},setValue:function(h){s=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Di(e){e._valueTracker||(e._valueTracker=at(e))}function xu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var i=t.getValue(),s="";return e&&(s=Pe(e)?e.checked?"true":"false":e.value),e=s,e!==i?(t.setValue(e),!0):!1}function Vi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function us(e,t){var i=t.checked;return B({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:i??e._wrapperState.initialChecked})}function wu(e,t){var i=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;i=he(t.value!=null?t.value:i),e._wrapperState={initialChecked:s,initialValue:i,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Su(e,t){t=t.checked,t!=null&&U(e,"checked",t,!1)}function cs(e,t){Su(e,t);var i=he(t.value),s=t.type;if(i!=null)s==="number"?(i===0&&e.value===""||e.value!=i)&&(e.value=""+i):e.value!==""+i&&(e.value=""+i);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?fs(e,t.type,i):t.hasOwnProperty("defaultValue")&&fs(e,t.type,he(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ku(e,t,i){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,i||t===e.value||(e.value=t),e.defaultValue=t}i=e.name,i!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,i!==""&&(e.name=i)}function fs(e,t,i){(t!=="number"||Vi(e.ownerDocument)!==e)&&(i==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+i&&(e.defaultValue=""+i))}var Er=Array.isArray;function Wn(e,t,i,s){if(e=e.options,t){t={};for(var a=0;a<i.length;a++)t["$"+i[a]]=!0;for(i=0;i<e.length;i++)a=t.hasOwnProperty("$"+e[i].value),e[i].selected!==a&&(e[i].selected=a),a&&s&&(e[i].defaultSelected=!0)}else{for(i=""+he(i),t=null,a=0;a<e.length;a++){if(e[a].value===i){e[a].selected=!0,s&&(e[a].defaultSelected=!0);return}t!==null||e[a].disabled||(t=e[a])}t!==null&&(t.selected=!0)}}function ds(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(o(91));return B({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Cu(e,t){var i=t.value;if(i==null){if(i=t.children,t=t.defaultValue,i!=null){if(t!=null)throw Error(o(92));if(Er(i)){if(1<i.length)throw Error(o(93));i=i[0]}t=i}t==null&&(t=""),i=t}e._wrapperState={initialValue:he(i)}}function Pu(e,t){var i=he(t.value),s=he(t.defaultValue);i!=null&&(i=""+i,i!==e.value&&(e.value=i),t.defaultValue==null&&e.defaultValue!==i&&(e.defaultValue=i)),s!=null&&(e.defaultValue=""+s)}function Tu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Eu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ps(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Eu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Li,Au=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,i,s,a){MSApp.execUnsafeLocalFunction(function(){return e(t,i,s,a)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Li=Li||document.createElement("div"),Li.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ar(e,t){if(t){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=t;return}}e.textContent=t}var Mr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Rm=["Webkit","ms","Moz","O"];Object.keys(Mr).forEach(function(e){Rm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Mr[t]=Mr[e]})});function Mu(e,t,i){return t==null||typeof t=="boolean"||t===""?"":i||typeof t!="number"||t===0||Mr.hasOwnProperty(e)&&Mr[e]?(""+t).trim():t+"px"}function Ru(e,t){e=e.style;for(var i in t)if(t.hasOwnProperty(i)){var s=i.indexOf("--")===0,a=Mu(i,t[i],s);i==="float"&&(i="cssFloat"),s?e.setProperty(i,a):e[i]=a}}var Nm=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function hs(e,t){if(t){if(Nm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(o(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(o(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(t.style!=null&&typeof t.style!="object")throw Error(o(62))}}function ms(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gs=null;function ys(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var vs=null,$n=null,Hn=null;function Nu(e){if(e=Xr(e)){if(typeof vs!="function")throw Error(o(280));var t=e.stateNode;t&&(t=no(t),vs(e.stateNode,e.type,t))}}function Du(e){$n?Hn?Hn.push(e):Hn=[e]:$n=e}function Vu(){if($n){var e=$n,t=Hn;if(Hn=$n=null,Nu(e),t)for(e=0;e<t.length;e++)Nu(t[e])}}function Lu(e,t){return e(t)}function _u(){}var xs=!1;function ju(e,t,i){if(xs)return e(t,i);xs=!0;try{return Lu(e,t,i)}finally{xs=!1,($n!==null||Hn!==null)&&(_u(),Vu())}}function Rr(e,t){var i=e.stateNode;if(i===null)return null;var s=no(i);if(s===null)return null;i=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(o(231,t,typeof i));return i}var ws=!1;if(p)try{var Nr={};Object.defineProperty(Nr,"passive",{get:function(){ws=!0}}),window.addEventListener("test",Nr,Nr),window.removeEventListener("test",Nr,Nr)}catch{ws=!1}function Dm(e,t,i,s,a,c,h,v,w){var E=Array.prototype.slice.call(arguments,3);try{t.apply(i,E)}catch(L){this.onError(L)}}var Dr=!1,_i=null,ji=!1,Ss=null,Vm={onError:function(e){Dr=!0,_i=e}};function Lm(e,t,i,s,a,c,h,v,w){Dr=!1,_i=null,Dm.apply(Vm,arguments)}function _m(e,t,i,s,a,c,h,v,w){if(Lm.apply(this,arguments),Dr){if(Dr){var E=_i;Dr=!1,_i=null}else throw Error(o(198));ji||(ji=!0,Ss=E)}}function Pn(e){var t=e,i=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(i=t.return),e=t.return;while(e)}return t.tag===3?i:null}function Iu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ou(e){if(Pn(e)!==e)throw Error(o(188))}function jm(e){var t=e.alternate;if(!t){if(t=Pn(e),t===null)throw Error(o(188));return t!==e?null:e}for(var i=e,s=t;;){var a=i.return;if(a===null)break;var c=a.alternate;if(c===null){if(s=a.return,s!==null){i=s;continue}break}if(a.child===c.child){for(c=a.child;c;){if(c===i)return Ou(a),e;if(c===s)return Ou(a),t;c=c.sibling}throw Error(o(188))}if(i.return!==s.return)i=a,s=c;else{for(var h=!1,v=a.child;v;){if(v===i){h=!0,i=a,s=c;break}if(v===s){h=!0,s=a,i=c;break}v=v.sibling}if(!h){for(v=c.child;v;){if(v===i){h=!0,i=c,s=a;break}if(v===s){h=!0,s=c,i=a;break}v=v.sibling}if(!h)throw Error(o(189))}}if(i.alternate!==s)throw Error(o(190))}if(i.tag!==3)throw Error(o(188));return i.stateNode.current===i?e:t}function zu(e){return e=jm(e),e!==null?Fu(e):null}function Fu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Fu(e);if(t!==null)return t;e=e.sibling}return null}var bu=r.unstable_scheduleCallback,Bu=r.unstable_cancelCallback,Im=r.unstable_shouldYield,Om=r.unstable_requestPaint,De=r.unstable_now,zm=r.unstable_getCurrentPriorityLevel,ks=r.unstable_ImmediatePriority,Uu=r.unstable_UserBlockingPriority,Ii=r.unstable_NormalPriority,Fm=r.unstable_LowPriority,Wu=r.unstable_IdlePriority,Oi=null,Lt=null;function bm(e){if(Lt&&typeof Lt.onCommitFiberRoot=="function")try{Lt.onCommitFiberRoot(Oi,e,void 0,(e.current.flags&128)===128)}catch{}}var Tt=Math.clz32?Math.clz32:Wm,Bm=Math.log,Um=Math.LN2;function Wm(e){return e>>>=0,e===0?32:31-(Bm(e)/Um|0)|0}var zi=64,Fi=4194304;function Vr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function bi(e,t){var i=e.pendingLanes;if(i===0)return 0;var s=0,a=e.suspendedLanes,c=e.pingedLanes,h=i&268435455;if(h!==0){var v=h&~a;v!==0?s=Vr(v):(c&=h,c!==0&&(s=Vr(c)))}else h=i&~a,h!==0?s=Vr(h):c!==0&&(s=Vr(c));if(s===0)return 0;if(t!==0&&t!==s&&(t&a)===0&&(a=s&-s,c=t&-t,a>=c||a===16&&(c&4194240)!==0))return t;if((s&4)!==0&&(s|=i&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)i=31-Tt(t),a=1<<i,s|=e[i],t&=~a;return s}function $m(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Hm(e,t){for(var i=e.suspendedLanes,s=e.pingedLanes,a=e.expirationTimes,c=e.pendingLanes;0<c;){var h=31-Tt(c),v=1<<h,w=a[h];w===-1?((v&i)===0||(v&s)!==0)&&(a[h]=$m(v,t)):w<=t&&(e.expiredLanes|=v),c&=~v}}function Cs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function $u(){var e=zi;return zi<<=1,(zi&4194240)===0&&(zi=64),e}function Ps(e){for(var t=[],i=0;31>i;i++)t.push(e);return t}function Lr(e,t,i){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Tt(t),e[t]=i}function Km(e,t){var i=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<i;){var a=31-Tt(i),c=1<<a;t[a]=0,s[a]=-1,e[a]=-1,i&=~c}}function Ts(e,t){var i=e.entangledLanes|=t;for(e=e.entanglements;i;){var s=31-Tt(i),a=1<<s;a&t|e[s]&t&&(e[s]|=t),i&=~a}}var me=0;function Hu(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Ku,Es,Gu,Qu,Yu,As=!1,Bi=[],qt=null,Jt=null,en=null,_r=new Map,jr=new Map,tn=[],Gm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Xu(e,t){switch(e){case"focusin":case"focusout":qt=null;break;case"dragenter":case"dragleave":Jt=null;break;case"mouseover":case"mouseout":en=null;break;case"pointerover":case"pointerout":_r.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":jr.delete(t.pointerId)}}function Ir(e,t,i,s,a,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:i,eventSystemFlags:s,nativeEvent:c,targetContainers:[a]},t!==null&&(t=Xr(t),t!==null&&Es(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,a!==null&&t.indexOf(a)===-1&&t.push(a),e)}function Qm(e,t,i,s,a){switch(t){case"focusin":return qt=Ir(qt,e,t,i,s,a),!0;case"dragenter":return Jt=Ir(Jt,e,t,i,s,a),!0;case"mouseover":return en=Ir(en,e,t,i,s,a),!0;case"pointerover":var c=a.pointerId;return _r.set(c,Ir(_r.get(c)||null,e,t,i,s,a)),!0;case"gotpointercapture":return c=a.pointerId,jr.set(c,Ir(jr.get(c)||null,e,t,i,s,a)),!0}return!1}function Zu(e){var t=Tn(e.target);if(t!==null){var i=Pn(t);if(i!==null){if(t=i.tag,t===13){if(t=Iu(i),t!==null){e.blockedOn=t,Yu(e.priority,function(){Gu(i)});return}}else if(t===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ui(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var i=Rs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(i===null){i=e.nativeEvent;var s=new i.constructor(i.type,i);gs=s,i.target.dispatchEvent(s),gs=null}else return t=Xr(i),t!==null&&Es(t),e.blockedOn=i,!1;t.shift()}return!0}function qu(e,t,i){Ui(e)&&i.delete(t)}function Ym(){As=!1,qt!==null&&Ui(qt)&&(qt=null),Jt!==null&&Ui(Jt)&&(Jt=null),en!==null&&Ui(en)&&(en=null),_r.forEach(qu),jr.forEach(qu)}function Or(e,t){e.blockedOn===t&&(e.blockedOn=null,As||(As=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ym)))}function zr(e){function t(a){return Or(a,e)}if(0<Bi.length){Or(Bi[0],e);for(var i=1;i<Bi.length;i++){var s=Bi[i];s.blockedOn===e&&(s.blockedOn=null)}}for(qt!==null&&Or(qt,e),Jt!==null&&Or(Jt,e),en!==null&&Or(en,e),_r.forEach(t),jr.forEach(t),i=0;i<tn.length;i++)s=tn[i],s.blockedOn===e&&(s.blockedOn=null);for(;0<tn.length&&(i=tn[0],i.blockedOn===null);)Zu(i),i.blockedOn===null&&tn.shift()}var Kn=q.ReactCurrentBatchConfig,Wi=!0;function Xm(e,t,i,s){var a=me,c=Kn.transition;Kn.transition=null;try{me=1,Ms(e,t,i,s)}finally{me=a,Kn.transition=c}}function Zm(e,t,i,s){var a=me,c=Kn.transition;Kn.transition=null;try{me=4,Ms(e,t,i,s)}finally{me=a,Kn.transition=c}}function Ms(e,t,i,s){if(Wi){var a=Rs(e,t,i,s);if(a===null)Ks(e,t,s,$i,i),Xu(e,s);else if(Qm(a,e,t,i,s))s.stopPropagation();else if(Xu(e,s),t&4&&-1<Gm.indexOf(e)){for(;a!==null;){var c=Xr(a);if(c!==null&&Ku(c),c=Rs(e,t,i,s),c===null&&Ks(e,t,s,$i,i),c===a)break;a=c}a!==null&&s.stopPropagation()}else Ks(e,t,s,null,i)}}var $i=null;function Rs(e,t,i,s){if($i=null,e=ys(s),e=Tn(e),e!==null)if(t=Pn(e),t===null)e=null;else if(i=t.tag,i===13){if(e=Iu(t),e!==null)return e;e=null}else if(i===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return $i=e,null}function Ju(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(zm()){case ks:return 1;case Uu:return 4;case Ii:case Fm:return 16;case Wu:return 536870912;default:return 16}default:return 16}}var nn=null,Ns=null,Hi=null;function ec(){if(Hi)return Hi;var e,t=Ns,i=t.length,s,a="value"in nn?nn.value:nn.textContent,c=a.length;for(e=0;e<i&&t[e]===a[e];e++);var h=i-e;for(s=1;s<=h&&t[i-s]===a[c-s];s++);return Hi=a.slice(e,1<s?1-s:void 0)}function Ki(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Gi(){return!0}function tc(){return!1}function ut(e){function t(i,s,a,c,h){this._reactName=i,this._targetInst=a,this.type=s,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(i=e[v],this[v]=i?i(c):c[v]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Gi:tc,this.isPropagationStopped=tc,this}return B(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=Gi)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=Gi)},persist:function(){},isPersistent:Gi}),t}var Gn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ds=ut(Gn),Fr=B({},Gn,{view:0,detail:0}),qm=ut(Fr),Vs,Ls,br,Qi=B({},Fr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:js,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==br&&(br&&e.type==="mousemove"?(Vs=e.screenX-br.screenX,Ls=e.screenY-br.screenY):Ls=Vs=0,br=e),Vs)},movementY:function(e){return"movementY"in e?e.movementY:Ls}}),nc=ut(Qi),Jm=B({},Qi,{dataTransfer:0}),eg=ut(Jm),tg=B({},Fr,{relatedTarget:0}),_s=ut(tg),ng=B({},Gn,{animationName:0,elapsedTime:0,pseudoElement:0}),rg=ut(ng),ig=B({},Gn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),og=ut(ig),sg=B({},Gn,{data:0}),rc=ut(sg),lg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ag={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ug={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ug[e])?!!t[e]:!1}function js(){return cg}var fg=B({},Fr,{key:function(e){if(e.key){var t=lg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ki(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ag[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:js,charCode:function(e){return e.type==="keypress"?Ki(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ki(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),dg=ut(fg),pg=B({},Qi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ic=ut(pg),hg=B({},Fr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:js}),mg=ut(hg),gg=B({},Gn,{propertyName:0,elapsedTime:0,pseudoElement:0}),yg=ut(gg),vg=B({},Qi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),xg=ut(vg),wg=[9,13,27,32],Is=p&&"CompositionEvent"in window,Br=null;p&&"documentMode"in document&&(Br=document.documentMode);var Sg=p&&"TextEvent"in window&&!Br,oc=p&&(!Is||Br&&8<Br&&11>=Br),sc=" ",lc=!1;function ac(e,t){switch(e){case"keyup":return wg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function uc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Qn=!1;function kg(e,t){switch(e){case"compositionend":return uc(t);case"keypress":return t.which!==32?null:(lc=!0,sc);case"textInput":return e=t.data,e===sc&&lc?null:e;default:return null}}function Cg(e,t){if(Qn)return e==="compositionend"||!Is&&ac(e,t)?(e=ec(),Hi=Ns=nn=null,Qn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return oc&&t.locale!=="ko"?null:t.data;default:return null}}var Pg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Pg[e.type]:t==="textarea"}function fc(e,t,i,s){Du(s),t=Ji(t,"onChange"),0<t.length&&(i=new Ds("onChange","change",null,i,s),e.push({event:i,listeners:t}))}var Ur=null,Wr=null;function Tg(e){Rc(e,0)}function Yi(e){var t=Jn(e);if(xu(t))return e}function Eg(e,t){if(e==="change")return t}var dc=!1;if(p){var Os;if(p){var zs="oninput"in document;if(!zs){var pc=document.createElement("div");pc.setAttribute("oninput","return;"),zs=typeof pc.oninput=="function"}Os=zs}else Os=!1;dc=Os&&(!document.documentMode||9<document.documentMode)}function hc(){Ur&&(Ur.detachEvent("onpropertychange",mc),Wr=Ur=null)}function mc(e){if(e.propertyName==="value"&&Yi(Wr)){var t=[];fc(t,Wr,e,ys(e)),ju(Tg,t)}}function Ag(e,t,i){e==="focusin"?(hc(),Ur=t,Wr=i,Ur.attachEvent("onpropertychange",mc)):e==="focusout"&&hc()}function Mg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Yi(Wr)}function Rg(e,t){if(e==="click")return Yi(t)}function Ng(e,t){if(e==="input"||e==="change")return Yi(t)}function Dg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:Dg;function $r(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var i=Object.keys(e),s=Object.keys(t);if(i.length!==s.length)return!1;for(s=0;s<i.length;s++){var a=i[s];if(!m.call(t,a)||!Et(e[a],t[a]))return!1}return!0}function gc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function yc(e,t){var i=gc(e);e=0;for(var s;i;){if(i.nodeType===3){if(s=e+i.textContent.length,e<=t&&s>=t)return{node:i,offset:t-e};e=s}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=gc(i)}}function vc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function xc(){for(var e=window,t=Vi();t instanceof e.HTMLIFrameElement;){try{var i=typeof t.contentWindow.location.href=="string"}catch{i=!1}if(i)e=t.contentWindow;else break;t=Vi(e.document)}return t}function Fs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Vg(e){var t=xc(),i=e.focusedElem,s=e.selectionRange;if(t!==i&&i&&i.ownerDocument&&vc(i.ownerDocument.documentElement,i)){if(s!==null&&Fs(i)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in i)i.selectionStart=t,i.selectionEnd=Math.min(e,i.value.length);else if(e=(t=i.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var a=i.textContent.length,c=Math.min(s.start,a);s=s.end===void 0?c:Math.min(s.end,a),!e.extend&&c>s&&(a=s,s=c,c=a),a=yc(i,c);var h=yc(i,s);a&&h&&(e.rangeCount!==1||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==h.node||e.focusOffset!==h.offset)&&(t=t.createRange(),t.setStart(a.node,a.offset),e.removeAllRanges(),c>s?(e.addRange(t),e.extend(h.node,h.offset)):(t.setEnd(h.node,h.offset),e.addRange(t)))}}for(t=[],e=i;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof i.focus=="function"&&i.focus(),i=0;i<t.length;i++)e=t[i],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Lg=p&&"documentMode"in document&&11>=document.documentMode,Yn=null,bs=null,Hr=null,Bs=!1;function wc(e,t,i){var s=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;Bs||Yn==null||Yn!==Vi(s)||(s=Yn,"selectionStart"in s&&Fs(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Hr&&$r(Hr,s)||(Hr=s,s=Ji(bs,"onSelect"),0<s.length&&(t=new Ds("onSelect","select",null,t,i),e.push({event:t,listeners:s}),t.target=Yn)))}function Xi(e,t){var i={};return i[e.toLowerCase()]=t.toLowerCase(),i["Webkit"+e]="webkit"+t,i["Moz"+e]="moz"+t,i}var Xn={animationend:Xi("Animation","AnimationEnd"),animationiteration:Xi("Animation","AnimationIteration"),animationstart:Xi("Animation","AnimationStart"),transitionend:Xi("Transition","TransitionEnd")},Us={},Sc={};p&&(Sc=document.createElement("div").style,"AnimationEvent"in window||(delete Xn.animationend.animation,delete Xn.animationiteration.animation,delete Xn.animationstart.animation),"TransitionEvent"in window||delete Xn.transitionend.transition);function Zi(e){if(Us[e])return Us[e];if(!Xn[e])return e;var t=Xn[e],i;for(i in t)if(t.hasOwnProperty(i)&&i in Sc)return Us[e]=t[i];return e}var kc=Zi("animationend"),Cc=Zi("animationiteration"),Pc=Zi("animationstart"),Tc=Zi("transitionend"),Ec=new Map,Ac="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function rn(e,t){Ec.set(e,t),d(t,[e])}for(var Ws=0;Ws<Ac.length;Ws++){var $s=Ac[Ws],_g=$s.toLowerCase(),jg=$s[0].toUpperCase()+$s.slice(1);rn(_g,"on"+jg)}rn(kc,"onAnimationEnd"),rn(Cc,"onAnimationIteration"),rn(Pc,"onAnimationStart"),rn("dblclick","onDoubleClick"),rn("focusin","onFocus"),rn("focusout","onBlur"),rn(Tc,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),d("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),d("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),d("onBeforeInput",["compositionend","keypress","textInput","paste"]),d("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ig=new Set("cancel close invalid load scroll toggle".split(" ").concat(Kr));function Mc(e,t,i){var s=e.type||"unknown-event";e.currentTarget=i,_m(s,t,void 0,e),e.currentTarget=null}function Rc(e,t){t=(t&4)!==0;for(var i=0;i<e.length;i++){var s=e[i],a=s.event;s=s.listeners;e:{var c=void 0;if(t)for(var h=s.length-1;0<=h;h--){var v=s[h],w=v.instance,E=v.currentTarget;if(v=v.listener,w!==c&&a.isPropagationStopped())break e;Mc(a,v,E),c=w}else for(h=0;h<s.length;h++){if(v=s[h],w=v.instance,E=v.currentTarget,v=v.listener,w!==c&&a.isPropagationStopped())break e;Mc(a,v,E),c=w}}}if(ji)throw e=Ss,ji=!1,Ss=null,e}function we(e,t){var i=t[qs];i===void 0&&(i=t[qs]=new Set);var s=e+"__bubble";i.has(s)||(Nc(t,e,2,!1),i.add(s))}function Hs(e,t,i){var s=0;t&&(s|=4),Nc(i,e,s,t)}var qi="_reactListening"+Math.random().toString(36).slice(2);function Gr(e){if(!e[qi]){e[qi]=!0,l.forEach(function(i){i!=="selectionchange"&&(Ig.has(i)||Hs(i,!1,e),Hs(i,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[qi]||(t[qi]=!0,Hs("selectionchange",!1,t))}}function Nc(e,t,i,s){switch(Ju(t)){case 1:var a=Xm;break;case 4:a=Zm;break;default:a=Ms}i=a.bind(null,t,i,e),a=void 0,!ws||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(a=!0),s?a!==void 0?e.addEventListener(t,i,{capture:!0,passive:a}):e.addEventListener(t,i,!0):a!==void 0?e.addEventListener(t,i,{passive:a}):e.addEventListener(t,i,!1)}function Ks(e,t,i,s,a){var c=s;if((t&1)===0&&(t&2)===0&&s!==null)e:for(;;){if(s===null)return;var h=s.tag;if(h===3||h===4){var v=s.stateNode.containerInfo;if(v===a||v.nodeType===8&&v.parentNode===a)break;if(h===4)for(h=s.return;h!==null;){var w=h.tag;if((w===3||w===4)&&(w=h.stateNode.containerInfo,w===a||w.nodeType===8&&w.parentNode===a))return;h=h.return}for(;v!==null;){if(h=Tn(v),h===null)return;if(w=h.tag,w===5||w===6){s=c=h;continue e}v=v.parentNode}}s=s.return}ju(function(){var E=c,L=ys(i),j=[];e:{var D=Ec.get(e);if(D!==void 0){var $=Ds,K=e;switch(e){case"keypress":if(Ki(i)===0)break e;case"keydown":case"keyup":$=dg;break;case"focusin":K="focus",$=_s;break;case"focusout":K="blur",$=_s;break;case"beforeblur":case"afterblur":$=_s;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":$=nc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":$=eg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":$=mg;break;case kc:case Cc:case Pc:$=rg;break;case Tc:$=yg;break;case"scroll":$=qm;break;case"wheel":$=xg;break;case"copy":case"cut":case"paste":$=og;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":$=ic}var Q=(t&4)!==0,Ve=!Q&&e==="scroll",P=Q?D!==null?D+"Capture":null:D;Q=[];for(var k=E,T;k!==null;){T=k;var O=T.stateNode;if(T.tag===5&&O!==null&&(T=O,P!==null&&(O=Rr(k,P),O!=null&&Q.push(Qr(k,O,T)))),Ve)break;k=k.return}0<Q.length&&(D=new $(D,K,null,i,L),j.push({event:D,listeners:Q}))}}if((t&7)===0){e:{if(D=e==="mouseover"||e==="pointerover",$=e==="mouseout"||e==="pointerout",D&&i!==gs&&(K=i.relatedTarget||i.fromElement)&&(Tn(K)||K[bt]))break e;if(($||D)&&(D=L.window===L?L:(D=L.ownerDocument)?D.defaultView||D.parentWindow:window,$?(K=i.relatedTarget||i.toElement,$=E,K=K?Tn(K):null,K!==null&&(Ve=Pn(K),K!==Ve||K.tag!==5&&K.tag!==6)&&(K=null)):($=null,K=E),$!==K)){if(Q=nc,O="onMouseLeave",P="onMouseEnter",k="mouse",(e==="pointerout"||e==="pointerover")&&(Q=ic,O="onPointerLeave",P="onPointerEnter",k="pointer"),Ve=$==null?D:Jn($),T=K==null?D:Jn(K),D=new Q(O,k+"leave",$,i,L),D.target=Ve,D.relatedTarget=T,O=null,Tn(L)===E&&(Q=new Q(P,k+"enter",K,i,L),Q.target=T,Q.relatedTarget=Ve,O=Q),Ve=O,$&&K)t:{for(Q=$,P=K,k=0,T=Q;T;T=Zn(T))k++;for(T=0,O=P;O;O=Zn(O))T++;for(;0<k-T;)Q=Zn(Q),k--;for(;0<T-k;)P=Zn(P),T--;for(;k--;){if(Q===P||P!==null&&Q===P.alternate)break t;Q=Zn(Q),P=Zn(P)}Q=null}else Q=null;$!==null&&Dc(j,D,$,Q,!1),K!==null&&Ve!==null&&Dc(j,Ve,K,Q,!0)}}e:{if(D=E?Jn(E):window,$=D.nodeName&&D.nodeName.toLowerCase(),$==="select"||$==="input"&&D.type==="file")var Y=Eg;else if(cc(D))if(dc)Y=Ng;else{Y=Mg;var J=Ag}else($=D.nodeName)&&$.toLowerCase()==="input"&&(D.type==="checkbox"||D.type==="radio")&&(Y=Rg);if(Y&&(Y=Y(e,E))){fc(j,Y,i,L);break e}J&&J(e,D,E),e==="focusout"&&(J=D._wrapperState)&&J.controlled&&D.type==="number"&&fs(D,"number",D.value)}switch(J=E?Jn(E):window,e){case"focusin":(cc(J)||J.contentEditable==="true")&&(Yn=J,bs=E,Hr=null);break;case"focusout":Hr=bs=Yn=null;break;case"mousedown":Bs=!0;break;case"contextmenu":case"mouseup":case"dragend":Bs=!1,wc(j,i,L);break;case"selectionchange":if(Lg)break;case"keydown":case"keyup":wc(j,i,L)}var ee;if(Is)e:{switch(e){case"compositionstart":var re="onCompositionStart";break e;case"compositionend":re="onCompositionEnd";break e;case"compositionupdate":re="onCompositionUpdate";break e}re=void 0}else Qn?ac(e,i)&&(re="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(re="onCompositionStart");re&&(oc&&i.locale!=="ko"&&(Qn||re!=="onCompositionStart"?re==="onCompositionEnd"&&Qn&&(ee=ec()):(nn=L,Ns="value"in nn?nn.value:nn.textContent,Qn=!0)),J=Ji(E,re),0<J.length&&(re=new rc(re,e,null,i,L),j.push({event:re,listeners:J}),ee?re.data=ee:(ee=uc(i),ee!==null&&(re.data=ee)))),(ee=Sg?kg(e,i):Cg(e,i))&&(E=Ji(E,"onBeforeInput"),0<E.length&&(L=new rc("onBeforeInput","beforeinput",null,i,L),j.push({event:L,listeners:E}),L.data=ee))}Rc(j,t)})}function Qr(e,t,i){return{instance:e,listener:t,currentTarget:i}}function Ji(e,t){for(var i=t+"Capture",s=[];e!==null;){var a=e,c=a.stateNode;a.tag===5&&c!==null&&(a=c,c=Rr(e,i),c!=null&&s.unshift(Qr(e,c,a)),c=Rr(e,t),c!=null&&s.push(Qr(e,c,a))),e=e.return}return s}function Zn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Dc(e,t,i,s,a){for(var c=t._reactName,h=[];i!==null&&i!==s;){var v=i,w=v.alternate,E=v.stateNode;if(w!==null&&w===s)break;v.tag===5&&E!==null&&(v=E,a?(w=Rr(i,c),w!=null&&h.unshift(Qr(i,w,v))):a||(w=Rr(i,c),w!=null&&h.push(Qr(i,w,v)))),i=i.return}h.length!==0&&e.push({event:t,listeners:h})}var Og=/\r\n?/g,zg=/\u0000|\uFFFD/g;function Vc(e){return(typeof e=="string"?e:""+e).replace(Og,`
`).replace(zg,"")}function eo(e,t,i){if(t=Vc(t),Vc(e)!==t&&i)throw Error(o(425))}function to(){}var Gs=null,Qs=null;function Ys(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Xs=typeof setTimeout=="function"?setTimeout:void 0,Fg=typeof clearTimeout=="function"?clearTimeout:void 0,Lc=typeof Promise=="function"?Promise:void 0,bg=typeof queueMicrotask=="function"?queueMicrotask:typeof Lc<"u"?function(e){return Lc.resolve(null).then(e).catch(Bg)}:Xs;function Bg(e){setTimeout(function(){throw e})}function Zs(e,t){var i=t,s=0;do{var a=i.nextSibling;if(e.removeChild(i),a&&a.nodeType===8)if(i=a.data,i==="/$"){if(s===0){e.removeChild(a),zr(t);return}s--}else i!=="$"&&i!=="$?"&&i!=="$!"||s++;i=a}while(i);zr(t)}function on(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function _c(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(t===0)return e;t--}else i==="/$"&&t++}e=e.previousSibling}return null}var qn=Math.random().toString(36).slice(2),_t="__reactFiber$"+qn,Yr="__reactProps$"+qn,bt="__reactContainer$"+qn,qs="__reactEvents$"+qn,Ug="__reactListeners$"+qn,Wg="__reactHandles$"+qn;function Tn(e){var t=e[_t];if(t)return t;for(var i=e.parentNode;i;){if(t=i[bt]||i[_t]){if(i=t.alternate,t.child!==null||i!==null&&i.child!==null)for(e=_c(e);e!==null;){if(i=e[_t])return i;e=_c(e)}return t}e=i,i=e.parentNode}return null}function Xr(e){return e=e[_t]||e[bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Jn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(o(33))}function no(e){return e[Yr]||null}var Js=[],er=-1;function sn(e){return{current:e}}function Se(e){0>er||(e.current=Js[er],Js[er]=null,er--)}function xe(e,t){er++,Js[er]=e.current,e.current=t}var ln={},Ge=sn(ln),nt=sn(!1),En=ln;function tr(e,t){var i=e.type.contextTypes;if(!i)return ln;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var a={},c;for(c in i)a[c]=t[c];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function rt(e){return e=e.childContextTypes,e!=null}function ro(){Se(nt),Se(Ge)}function jc(e,t,i){if(Ge.current!==ln)throw Error(o(168));xe(Ge,t),xe(nt,i)}function Ic(e,t,i){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return i;s=s.getChildContext();for(var a in s)if(!(a in t))throw Error(o(108,ve(e)||"Unknown",a));return B({},i,s)}function io(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ln,En=Ge.current,xe(Ge,e),xe(nt,nt.current),!0}function Oc(e,t,i){var s=e.stateNode;if(!s)throw Error(o(169));i?(e=Ic(e,t,En),s.__reactInternalMemoizedMergedChildContext=e,Se(nt),Se(Ge),xe(Ge,e)):Se(nt),xe(nt,i)}var Bt=null,oo=!1,el=!1;function zc(e){Bt===null?Bt=[e]:Bt.push(e)}function $g(e){oo=!0,zc(e)}function an(){if(!el&&Bt!==null){el=!0;var e=0,t=me;try{var i=Bt;for(me=1;e<i.length;e++){var s=i[e];do s=s(!0);while(s!==null)}Bt=null,oo=!1}catch(a){throw Bt!==null&&(Bt=Bt.slice(e+1)),bu(ks,an),a}finally{me=t,el=!1}}return null}var nr=[],rr=0,so=null,lo=0,gt=[],yt=0,An=null,Ut=1,Wt="";function Mn(e,t){nr[rr++]=lo,nr[rr++]=so,so=e,lo=t}function Fc(e,t,i){gt[yt++]=Ut,gt[yt++]=Wt,gt[yt++]=An,An=e;var s=Ut;e=Wt;var a=32-Tt(s)-1;s&=~(1<<a),i+=1;var c=32-Tt(t)+a;if(30<c){var h=a-a%5;c=(s&(1<<h)-1).toString(32),s>>=h,a-=h,Ut=1<<32-Tt(t)+a|i<<a|s,Wt=c+e}else Ut=1<<c|i<<a|s,Wt=e}function tl(e){e.return!==null&&(Mn(e,1),Fc(e,1,0))}function nl(e){for(;e===so;)so=nr[--rr],nr[rr]=null,lo=nr[--rr],nr[rr]=null;for(;e===An;)An=gt[--yt],gt[yt]=null,Wt=gt[--yt],gt[yt]=null,Ut=gt[--yt],gt[yt]=null}var ct=null,ft=null,Te=!1,At=null;function bc(e,t){var i=St(5,null,null,0);i.elementType="DELETED",i.stateNode=t,i.return=e,t=e.deletions,t===null?(e.deletions=[i],e.flags|=16):t.push(i)}function Bc(e,t){switch(e.tag){case 5:var i=e.type;return t=t.nodeType!==1||i.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ct=e,ft=on(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ct=e,ft=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(i=An!==null?{id:Ut,overflow:Wt}:null,e.memoizedState={dehydrated:t,treeContext:i,retryLane:1073741824},i=St(18,null,null,0),i.stateNode=t,i.return=e,e.child=i,ct=e,ft=null,!0):!1;default:return!1}}function rl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function il(e){if(Te){var t=ft;if(t){var i=t;if(!Bc(e,t)){if(rl(e))throw Error(o(418));t=on(i.nextSibling);var s=ct;t&&Bc(e,t)?bc(s,i):(e.flags=e.flags&-4097|2,Te=!1,ct=e)}}else{if(rl(e))throw Error(o(418));e.flags=e.flags&-4097|2,Te=!1,ct=e}}}function Uc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ct=e}function ao(e){if(e!==ct)return!1;if(!Te)return Uc(e),Te=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ys(e.type,e.memoizedProps)),t&&(t=ft)){if(rl(e))throw Wc(),Error(o(418));for(;t;)bc(e,t),t=on(t.nextSibling)}if(Uc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var i=e.data;if(i==="/$"){if(t===0){ft=on(e.nextSibling);break e}t--}else i!=="$"&&i!=="$!"&&i!=="$?"||t++}e=e.nextSibling}ft=null}}else ft=ct?on(e.stateNode.nextSibling):null;return!0}function Wc(){for(var e=ft;e;)e=on(e.nextSibling)}function ir(){ft=ct=null,Te=!1}function ol(e){At===null?At=[e]:At.push(e)}var Hg=q.ReactCurrentBatchConfig;function Zr(e,t,i){if(e=i.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(i._owner){if(i=i._owner,i){if(i.tag!==1)throw Error(o(309));var s=i.stateNode}if(!s)throw Error(o(147,e));var a=s,c=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===c?t.ref:(t=function(h){var v=a.refs;h===null?delete v[c]:v[c]=h},t._stringRef=c,t)}if(typeof e!="string")throw Error(o(284));if(!i._owner)throw Error(o(290,e))}return e}function uo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function $c(e){var t=e._init;return t(e._payload)}function Hc(e){function t(P,k){if(e){var T=P.deletions;T===null?(P.deletions=[k],P.flags|=16):T.push(k)}}function i(P,k){if(!e)return null;for(;k!==null;)t(P,k),k=k.sibling;return null}function s(P,k){for(P=new Map;k!==null;)k.key!==null?P.set(k.key,k):P.set(k.index,k),k=k.sibling;return P}function a(P,k){return P=gn(P,k),P.index=0,P.sibling=null,P}function c(P,k,T){return P.index=T,e?(T=P.alternate,T!==null?(T=T.index,T<k?(P.flags|=2,k):T):(P.flags|=2,k)):(P.flags|=1048576,k)}function h(P){return e&&P.alternate===null&&(P.flags|=2),P}function v(P,k,T,O){return k===null||k.tag!==6?(k=Xl(T,P.mode,O),k.return=P,k):(k=a(k,T),k.return=P,k)}function w(P,k,T,O){var Y=T.type;return Y===ne?L(P,k,T.props.children,O,T.key):k!==null&&(k.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===_e&&$c(Y)===k.type)?(O=a(k,T.props),O.ref=Zr(P,k,T),O.return=P,O):(O=_o(T.type,T.key,T.props,null,P.mode,O),O.ref=Zr(P,k,T),O.return=P,O)}function E(P,k,T,O){return k===null||k.tag!==4||k.stateNode.containerInfo!==T.containerInfo||k.stateNode.implementation!==T.implementation?(k=Zl(T,P.mode,O),k.return=P,k):(k=a(k,T.children||[]),k.return=P,k)}function L(P,k,T,O,Y){return k===null||k.tag!==7?(k=In(T,P.mode,O,Y),k.return=P,k):(k=a(k,T),k.return=P,k)}function j(P,k,T){if(typeof k=="string"&&k!==""||typeof k=="number")return k=Xl(""+k,P.mode,T),k.return=P,k;if(typeof k=="object"&&k!==null){switch(k.$$typeof){case G:return T=_o(k.type,k.key,k.props,null,P.mode,T),T.ref=Zr(P,null,k),T.return=P,T;case W:return k=Zl(k,P.mode,T),k.return=P,k;case _e:var O=k._init;return j(P,O(k._payload),T)}if(Er(k)||Z(k))return k=In(k,P.mode,T,null),k.return=P,k;uo(P,k)}return null}function D(P,k,T,O){var Y=k!==null?k.key:null;if(typeof T=="string"&&T!==""||typeof T=="number")return Y!==null?null:v(P,k,""+T,O);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case G:return T.key===Y?w(P,k,T,O):null;case W:return T.key===Y?E(P,k,T,O):null;case _e:return Y=T._init,D(P,k,Y(T._payload),O)}if(Er(T)||Z(T))return Y!==null?null:L(P,k,T,O,null);uo(P,T)}return null}function $(P,k,T,O,Y){if(typeof O=="string"&&O!==""||typeof O=="number")return P=P.get(T)||null,v(k,P,""+O,Y);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case G:return P=P.get(O.key===null?T:O.key)||null,w(k,P,O,Y);case W:return P=P.get(O.key===null?T:O.key)||null,E(k,P,O,Y);case _e:var J=O._init;return $(P,k,T,J(O._payload),Y)}if(Er(O)||Z(O))return P=P.get(T)||null,L(k,P,O,Y,null);uo(k,O)}return null}function K(P,k,T,O){for(var Y=null,J=null,ee=k,re=k=0,We=null;ee!==null&&re<T.length;re++){ee.index>re?(We=ee,ee=null):We=ee.sibling;var pe=D(P,ee,T[re],O);if(pe===null){ee===null&&(ee=We);break}e&&ee&&pe.alternate===null&&t(P,ee),k=c(pe,k,re),J===null?Y=pe:J.sibling=pe,J=pe,ee=We}if(re===T.length)return i(P,ee),Te&&Mn(P,re),Y;if(ee===null){for(;re<T.length;re++)ee=j(P,T[re],O),ee!==null&&(k=c(ee,k,re),J===null?Y=ee:J.sibling=ee,J=ee);return Te&&Mn(P,re),Y}for(ee=s(P,ee);re<T.length;re++)We=$(ee,P,re,T[re],O),We!==null&&(e&&We.alternate!==null&&ee.delete(We.key===null?re:We.key),k=c(We,k,re),J===null?Y=We:J.sibling=We,J=We);return e&&ee.forEach(function(yn){return t(P,yn)}),Te&&Mn(P,re),Y}function Q(P,k,T,O){var Y=Z(T);if(typeof Y!="function")throw Error(o(150));if(T=Y.call(T),T==null)throw Error(o(151));for(var J=Y=null,ee=k,re=k=0,We=null,pe=T.next();ee!==null&&!pe.done;re++,pe=T.next()){ee.index>re?(We=ee,ee=null):We=ee.sibling;var yn=D(P,ee,pe.value,O);if(yn===null){ee===null&&(ee=We);break}e&&ee&&yn.alternate===null&&t(P,ee),k=c(yn,k,re),J===null?Y=yn:J.sibling=yn,J=yn,ee=We}if(pe.done)return i(P,ee),Te&&Mn(P,re),Y;if(ee===null){for(;!pe.done;re++,pe=T.next())pe=j(P,pe.value,O),pe!==null&&(k=c(pe,k,re),J===null?Y=pe:J.sibling=pe,J=pe);return Te&&Mn(P,re),Y}for(ee=s(P,ee);!pe.done;re++,pe=T.next())pe=$(ee,P,re,pe.value,O),pe!==null&&(e&&pe.alternate!==null&&ee.delete(pe.key===null?re:pe.key),k=c(pe,k,re),J===null?Y=pe:J.sibling=pe,J=pe);return e&&ee.forEach(function(Py){return t(P,Py)}),Te&&Mn(P,re),Y}function Ve(P,k,T,O){if(typeof T=="object"&&T!==null&&T.type===ne&&T.key===null&&(T=T.props.children),typeof T=="object"&&T!==null){switch(T.$$typeof){case G:e:{for(var Y=T.key,J=k;J!==null;){if(J.key===Y){if(Y=T.type,Y===ne){if(J.tag===7){i(P,J.sibling),k=a(J,T.props.children),k.return=P,P=k;break e}}else if(J.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===_e&&$c(Y)===J.type){i(P,J.sibling),k=a(J,T.props),k.ref=Zr(P,J,T),k.return=P,P=k;break e}i(P,J);break}else t(P,J);J=J.sibling}T.type===ne?(k=In(T.props.children,P.mode,O,T.key),k.return=P,P=k):(O=_o(T.type,T.key,T.props,null,P.mode,O),O.ref=Zr(P,k,T),O.return=P,P=O)}return h(P);case W:e:{for(J=T.key;k!==null;){if(k.key===J)if(k.tag===4&&k.stateNode.containerInfo===T.containerInfo&&k.stateNode.implementation===T.implementation){i(P,k.sibling),k=a(k,T.children||[]),k.return=P,P=k;break e}else{i(P,k);break}else t(P,k);k=k.sibling}k=Zl(T,P.mode,O),k.return=P,P=k}return h(P);case _e:return J=T._init,Ve(P,k,J(T._payload),O)}if(Er(T))return K(P,k,T,O);if(Z(T))return Q(P,k,T,O);uo(P,T)}return typeof T=="string"&&T!==""||typeof T=="number"?(T=""+T,k!==null&&k.tag===6?(i(P,k.sibling),k=a(k,T),k.return=P,P=k):(i(P,k),k=Xl(T,P.mode,O),k.return=P,P=k),h(P)):i(P,k)}return Ve}var or=Hc(!0),Kc=Hc(!1),co=sn(null),fo=null,sr=null,sl=null;function ll(){sl=sr=fo=null}function al(e){var t=co.current;Se(co),e._currentValue=t}function ul(e,t,i){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===i)break;e=e.return}}function lr(e,t){fo=e,sl=sr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(it=!0),e.firstContext=null)}function vt(e){var t=e._currentValue;if(sl!==e)if(e={context:e,memoizedValue:t,next:null},sr===null){if(fo===null)throw Error(o(308));sr=e,fo.dependencies={lanes:0,firstContext:e}}else sr=sr.next=e;return t}var Rn=null;function cl(e){Rn===null?Rn=[e]:Rn.push(e)}function Gc(e,t,i,s){var a=t.interleaved;return a===null?(i.next=i,cl(t)):(i.next=a.next,a.next=i),t.interleaved=i,$t(e,s)}function $t(e,t){e.lanes|=t;var i=e.alternate;for(i!==null&&(i.lanes|=t),i=e,e=e.return;e!==null;)e.childLanes|=t,i=e.alternate,i!==null&&(i.childLanes|=t),i=e,e=e.return;return i.tag===3?i.stateNode:null}var un=!1;function fl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Qc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ht(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function cn(e,t,i){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,(ce&2)!==0){var a=s.pending;return a===null?t.next=t:(t.next=a.next,a.next=t),s.pending=t,$t(e,i)}return a=s.interleaved,a===null?(t.next=t,cl(s)):(t.next=a.next,a.next=t),s.interleaved=t,$t(e,i)}function po(e,t,i){if(t=t.updateQueue,t!==null&&(t=t.shared,(i&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,i|=s,t.lanes=i,Ts(e,i)}}function Yc(e,t){var i=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,i===s)){var a=null,c=null;if(i=i.firstBaseUpdate,i!==null){do{var h={eventTime:i.eventTime,lane:i.lane,tag:i.tag,payload:i.payload,callback:i.callback,next:null};c===null?a=c=h:c=c.next=h,i=i.next}while(i!==null);c===null?a=c=t:c=c.next=t}else a=c=t;i={baseState:s.baseState,firstBaseUpdate:a,lastBaseUpdate:c,shared:s.shared,effects:s.effects},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=t:e.next=t,i.lastBaseUpdate=t}function ho(e,t,i,s){var a=e.updateQueue;un=!1;var c=a.firstBaseUpdate,h=a.lastBaseUpdate,v=a.shared.pending;if(v!==null){a.shared.pending=null;var w=v,E=w.next;w.next=null,h===null?c=E:h.next=E,h=w;var L=e.alternate;L!==null&&(L=L.updateQueue,v=L.lastBaseUpdate,v!==h&&(v===null?L.firstBaseUpdate=E:v.next=E,L.lastBaseUpdate=w))}if(c!==null){var j=a.baseState;h=0,L=E=w=null,v=c;do{var D=v.lane,$=v.eventTime;if((s&D)===D){L!==null&&(L=L.next={eventTime:$,lane:0,tag:v.tag,payload:v.payload,callback:v.callback,next:null});e:{var K=e,Q=v;switch(D=t,$=i,Q.tag){case 1:if(K=Q.payload,typeof K=="function"){j=K.call($,j,D);break e}j=K;break e;case 3:K.flags=K.flags&-65537|128;case 0:if(K=Q.payload,D=typeof K=="function"?K.call($,j,D):K,D==null)break e;j=B({},j,D);break e;case 2:un=!0}}v.callback!==null&&v.lane!==0&&(e.flags|=64,D=a.effects,D===null?a.effects=[v]:D.push(v))}else $={eventTime:$,lane:D,tag:v.tag,payload:v.payload,callback:v.callback,next:null},L===null?(E=L=$,w=j):L=L.next=$,h|=D;if(v=v.next,v===null){if(v=a.shared.pending,v===null)break;D=v,v=D.next,D.next=null,a.lastBaseUpdate=D,a.shared.pending=null}}while(!0);if(L===null&&(w=j),a.baseState=w,a.firstBaseUpdate=E,a.lastBaseUpdate=L,t=a.shared.interleaved,t!==null){a=t;do h|=a.lane,a=a.next;while(a!==t)}else c===null&&(a.shared.lanes=0);Vn|=h,e.lanes=h,e.memoizedState=j}}function Xc(e,t,i){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],a=s.callback;if(a!==null){if(s.callback=null,s=i,typeof a!="function")throw Error(o(191,a));a.call(s)}}}var qr={},jt=sn(qr),Jr=sn(qr),ei=sn(qr);function Nn(e){if(e===qr)throw Error(o(174));return e}function dl(e,t){switch(xe(ei,t),xe(Jr,e),xe(jt,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ps(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ps(t,e)}Se(jt),xe(jt,t)}function ar(){Se(jt),Se(Jr),Se(ei)}function Zc(e){Nn(ei.current);var t=Nn(jt.current),i=ps(t,e.type);t!==i&&(xe(Jr,e),xe(jt,i))}function pl(e){Jr.current===e&&(Se(jt),Se(Jr))}var Ee=sn(0);function mo(e){for(var t=e;t!==null;){if(t.tag===13){var i=t.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var hl=[];function ml(){for(var e=0;e<hl.length;e++)hl[e]._workInProgressVersionPrimary=null;hl.length=0}var go=q.ReactCurrentDispatcher,gl=q.ReactCurrentBatchConfig,Dn=0,Ae=null,Oe=null,Be=null,yo=!1,ti=!1,ni=0,Kg=0;function Qe(){throw Error(o(321))}function yl(e,t){if(t===null)return!1;for(var i=0;i<t.length&&i<e.length;i++)if(!Et(e[i],t[i]))return!1;return!0}function vl(e,t,i,s,a,c){if(Dn=c,Ae=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,go.current=e===null||e.memoizedState===null?Xg:Zg,e=i(s,a),ti){c=0;do{if(ti=!1,ni=0,25<=c)throw Error(o(301));c+=1,Be=Oe=null,t.updateQueue=null,go.current=qg,e=i(s,a)}while(ti)}if(go.current=wo,t=Oe!==null&&Oe.next!==null,Dn=0,Be=Oe=Ae=null,yo=!1,t)throw Error(o(300));return e}function xl(){var e=ni!==0;return ni=0,e}function It(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Be===null?Ae.memoizedState=Be=e:Be=Be.next=e,Be}function xt(){if(Oe===null){var e=Ae.alternate;e=e!==null?e.memoizedState:null}else e=Oe.next;var t=Be===null?Ae.memoizedState:Be.next;if(t!==null)Be=t,Oe=e;else{if(e===null)throw Error(o(310));Oe=e,e={memoizedState:Oe.memoizedState,baseState:Oe.baseState,baseQueue:Oe.baseQueue,queue:Oe.queue,next:null},Be===null?Ae.memoizedState=Be=e:Be=Be.next=e}return Be}function ri(e,t){return typeof t=="function"?t(e):t}function wl(e){var t=xt(),i=t.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=e;var s=Oe,a=s.baseQueue,c=i.pending;if(c!==null){if(a!==null){var h=a.next;a.next=c.next,c.next=h}s.baseQueue=a=c,i.pending=null}if(a!==null){c=a.next,s=s.baseState;var v=h=null,w=null,E=c;do{var L=E.lane;if((Dn&L)===L)w!==null&&(w=w.next={lane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),s=E.hasEagerState?E.eagerState:e(s,E.action);else{var j={lane:L,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null};w===null?(v=w=j,h=s):w=w.next=j,Ae.lanes|=L,Vn|=L}E=E.next}while(E!==null&&E!==c);w===null?h=s:w.next=v,Et(s,t.memoizedState)||(it=!0),t.memoizedState=s,t.baseState=h,t.baseQueue=w,i.lastRenderedState=s}if(e=i.interleaved,e!==null){a=e;do c=a.lane,Ae.lanes|=c,Vn|=c,a=a.next;while(a!==e)}else a===null&&(i.lanes=0);return[t.memoizedState,i.dispatch]}function Sl(e){var t=xt(),i=t.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=e;var s=i.dispatch,a=i.pending,c=t.memoizedState;if(a!==null){i.pending=null;var h=a=a.next;do c=e(c,h.action),h=h.next;while(h!==a);Et(c,t.memoizedState)||(it=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),i.lastRenderedState=c}return[c,s]}function qc(){}function Jc(e,t){var i=Ae,s=xt(),a=t(),c=!Et(s.memoizedState,a);if(c&&(s.memoizedState=a,it=!0),s=s.queue,kl(nf.bind(null,i,s,e),[e]),s.getSnapshot!==t||c||Be!==null&&Be.memoizedState.tag&1){if(i.flags|=2048,ii(9,tf.bind(null,i,s,a,t),void 0,null),Ue===null)throw Error(o(349));(Dn&30)!==0||ef(i,t,a)}return a}function ef(e,t,i){e.flags|=16384,e={getSnapshot:t,value:i},t=Ae.updateQueue,t===null?(t={lastEffect:null,stores:null},Ae.updateQueue=t,t.stores=[e]):(i=t.stores,i===null?t.stores=[e]:i.push(e))}function tf(e,t,i,s){t.value=i,t.getSnapshot=s,rf(t)&&of(e)}function nf(e,t,i){return i(function(){rf(t)&&of(e)})}function rf(e){var t=e.getSnapshot;e=e.value;try{var i=t();return!Et(e,i)}catch{return!0}}function of(e){var t=$t(e,1);t!==null&&Dt(t,e,1,-1)}function sf(e){var t=It();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ri,lastRenderedState:e},t.queue=e,e=e.dispatch=Yg.bind(null,Ae,e),[t.memoizedState,e]}function ii(e,t,i,s){return e={tag:e,create:t,destroy:i,deps:s,next:null},t=Ae.updateQueue,t===null?(t={lastEffect:null,stores:null},Ae.updateQueue=t,t.lastEffect=e.next=e):(i=t.lastEffect,i===null?t.lastEffect=e.next=e:(s=i.next,i.next=e,e.next=s,t.lastEffect=e)),e}function lf(){return xt().memoizedState}function vo(e,t,i,s){var a=It();Ae.flags|=e,a.memoizedState=ii(1|t,i,void 0,s===void 0?null:s)}function xo(e,t,i,s){var a=xt();s=s===void 0?null:s;var c=void 0;if(Oe!==null){var h=Oe.memoizedState;if(c=h.destroy,s!==null&&yl(s,h.deps)){a.memoizedState=ii(t,i,c,s);return}}Ae.flags|=e,a.memoizedState=ii(1|t,i,c,s)}function af(e,t){return vo(8390656,8,e,t)}function kl(e,t){return xo(2048,8,e,t)}function uf(e,t){return xo(4,2,e,t)}function cf(e,t){return xo(4,4,e,t)}function ff(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function df(e,t,i){return i=i!=null?i.concat([e]):null,xo(4,4,ff.bind(null,t,e),i)}function Cl(){}function pf(e,t){var i=xt();t=t===void 0?null:t;var s=i.memoizedState;return s!==null&&t!==null&&yl(t,s[1])?s[0]:(i.memoizedState=[e,t],e)}function hf(e,t){var i=xt();t=t===void 0?null:t;var s=i.memoizedState;return s!==null&&t!==null&&yl(t,s[1])?s[0]:(e=e(),i.memoizedState=[e,t],e)}function mf(e,t,i){return(Dn&21)===0?(e.baseState&&(e.baseState=!1,it=!0),e.memoizedState=i):(Et(i,t)||(i=$u(),Ae.lanes|=i,Vn|=i,e.baseState=!0),t)}function Gg(e,t){var i=me;me=i!==0&&4>i?i:4,e(!0);var s=gl.transition;gl.transition={};try{e(!1),t()}finally{me=i,gl.transition=s}}function gf(){return xt().memoizedState}function Qg(e,t,i){var s=hn(e);if(i={lane:s,action:i,hasEagerState:!1,eagerState:null,next:null},yf(e))vf(t,i);else if(i=Gc(e,t,i,s),i!==null){var a=et();Dt(i,e,s,a),xf(i,t,s)}}function Yg(e,t,i){var s=hn(e),a={lane:s,action:i,hasEagerState:!1,eagerState:null,next:null};if(yf(e))vf(t,a);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,v=c(h,i);if(a.hasEagerState=!0,a.eagerState=v,Et(v,h)){var w=t.interleaved;w===null?(a.next=a,cl(t)):(a.next=w.next,w.next=a),t.interleaved=a;return}}catch{}finally{}i=Gc(e,t,a,s),i!==null&&(a=et(),Dt(i,e,s,a),xf(i,t,s))}}function yf(e){var t=e.alternate;return e===Ae||t!==null&&t===Ae}function vf(e,t){ti=yo=!0;var i=e.pending;i===null?t.next=t:(t.next=i.next,i.next=t),e.pending=t}function xf(e,t,i){if((i&4194240)!==0){var s=t.lanes;s&=e.pendingLanes,i|=s,t.lanes=i,Ts(e,i)}}var wo={readContext:vt,useCallback:Qe,useContext:Qe,useEffect:Qe,useImperativeHandle:Qe,useInsertionEffect:Qe,useLayoutEffect:Qe,useMemo:Qe,useReducer:Qe,useRef:Qe,useState:Qe,useDebugValue:Qe,useDeferredValue:Qe,useTransition:Qe,useMutableSource:Qe,useSyncExternalStore:Qe,useId:Qe,unstable_isNewReconciler:!1},Xg={readContext:vt,useCallback:function(e,t){return It().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:af,useImperativeHandle:function(e,t,i){return i=i!=null?i.concat([e]):null,vo(4194308,4,ff.bind(null,t,e),i)},useLayoutEffect:function(e,t){return vo(4194308,4,e,t)},useInsertionEffect:function(e,t){return vo(4,2,e,t)},useMemo:function(e,t){var i=It();return t=t===void 0?null:t,e=e(),i.memoizedState=[e,t],e},useReducer:function(e,t,i){var s=It();return t=i!==void 0?i(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=Qg.bind(null,Ae,e),[s.memoizedState,e]},useRef:function(e){var t=It();return e={current:e},t.memoizedState=e},useState:sf,useDebugValue:Cl,useDeferredValue:function(e){return It().memoizedState=e},useTransition:function(){var e=sf(!1),t=e[0];return e=Gg.bind(null,e[1]),It().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,i){var s=Ae,a=It();if(Te){if(i===void 0)throw Error(o(407));i=i()}else{if(i=t(),Ue===null)throw Error(o(349));(Dn&30)!==0||ef(s,t,i)}a.memoizedState=i;var c={value:i,getSnapshot:t};return a.queue=c,af(nf.bind(null,s,c,e),[e]),s.flags|=2048,ii(9,tf.bind(null,s,c,i,t),void 0,null),i},useId:function(){var e=It(),t=Ue.identifierPrefix;if(Te){var i=Wt,s=Ut;i=(s&~(1<<32-Tt(s)-1)).toString(32)+i,t=":"+t+"R"+i,i=ni++,0<i&&(t+="H"+i.toString(32)),t+=":"}else i=Kg++,t=":"+t+"r"+i.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Zg={readContext:vt,useCallback:pf,useContext:vt,useEffect:kl,useImperativeHandle:df,useInsertionEffect:uf,useLayoutEffect:cf,useMemo:hf,useReducer:wl,useRef:lf,useState:function(){return wl(ri)},useDebugValue:Cl,useDeferredValue:function(e){var t=xt();return mf(t,Oe.memoizedState,e)},useTransition:function(){var e=wl(ri)[0],t=xt().memoizedState;return[e,t]},useMutableSource:qc,useSyncExternalStore:Jc,useId:gf,unstable_isNewReconciler:!1},qg={readContext:vt,useCallback:pf,useContext:vt,useEffect:kl,useImperativeHandle:df,useInsertionEffect:uf,useLayoutEffect:cf,useMemo:hf,useReducer:Sl,useRef:lf,useState:function(){return Sl(ri)},useDebugValue:Cl,useDeferredValue:function(e){var t=xt();return Oe===null?t.memoizedState=e:mf(t,Oe.memoizedState,e)},useTransition:function(){var e=Sl(ri)[0],t=xt().memoizedState;return[e,t]},useMutableSource:qc,useSyncExternalStore:Jc,useId:gf,unstable_isNewReconciler:!1};function Mt(e,t){if(e&&e.defaultProps){t=B({},t),e=e.defaultProps;for(var i in e)t[i]===void 0&&(t[i]=e[i]);return t}return t}function Pl(e,t,i,s){t=e.memoizedState,i=i(s,t),i=i==null?t:B({},t,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var So={isMounted:function(e){return(e=e._reactInternals)?Pn(e)===e:!1},enqueueSetState:function(e,t,i){e=e._reactInternals;var s=et(),a=hn(e),c=Ht(s,a);c.payload=t,i!=null&&(c.callback=i),t=cn(e,c,a),t!==null&&(Dt(t,e,a,s),po(t,e,a))},enqueueReplaceState:function(e,t,i){e=e._reactInternals;var s=et(),a=hn(e),c=Ht(s,a);c.tag=1,c.payload=t,i!=null&&(c.callback=i),t=cn(e,c,a),t!==null&&(Dt(t,e,a,s),po(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var i=et(),s=hn(e),a=Ht(i,s);a.tag=2,t!=null&&(a.callback=t),t=cn(e,a,s),t!==null&&(Dt(t,e,s,i),po(t,e,s))}};function wf(e,t,i,s,a,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,c,h):t.prototype&&t.prototype.isPureReactComponent?!$r(i,s)||!$r(a,c):!0}function Sf(e,t,i){var s=!1,a=ln,c=t.contextType;return typeof c=="object"&&c!==null?c=vt(c):(a=rt(t)?En:Ge.current,s=t.contextTypes,c=(s=s!=null)?tr(e,a):ln),t=new t(i,c),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=So,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=c),t}function kf(e,t,i,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(i,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(i,s),t.state!==e&&So.enqueueReplaceState(t,t.state,null)}function Tl(e,t,i,s){var a=e.stateNode;a.props=i,a.state=e.memoizedState,a.refs={},fl(e);var c=t.contextType;typeof c=="object"&&c!==null?a.context=vt(c):(c=rt(t)?En:Ge.current,a.context=tr(e,c)),a.state=e.memoizedState,c=t.getDerivedStateFromProps,typeof c=="function"&&(Pl(e,t,c,i),a.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(t=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),t!==a.state&&So.enqueueReplaceState(a,a.state,null),ho(e,i,a,s),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308)}function ur(e,t){try{var i="",s=t;do i+=fe(s),s=s.return;while(s);var a=i}catch(c){a=`
Error generating stack: `+c.message+`
`+c.stack}return{value:e,source:t,stack:a,digest:null}}function El(e,t,i){return{value:e,source:null,stack:i??null,digest:t??null}}function Al(e,t){try{console.error(t.value)}catch(i){setTimeout(function(){throw i})}}var Jg=typeof WeakMap=="function"?WeakMap:Map;function Cf(e,t,i){i=Ht(-1,i),i.tag=3,i.payload={element:null};var s=t.value;return i.callback=function(){Mo||(Mo=!0,Ul=s),Al(e,t)},i}function Pf(e,t,i){i=Ht(-1,i),i.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var a=t.value;i.payload=function(){return s(a)},i.callback=function(){Al(e,t)}}var c=e.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(i.callback=function(){Al(e,t),typeof s!="function"&&(dn===null?dn=new Set([this]):dn.add(this));var h=t.stack;this.componentDidCatch(t.value,{componentStack:h!==null?h:""})}),i}function Tf(e,t,i){var s=e.pingCache;if(s===null){s=e.pingCache=new Jg;var a=new Set;s.set(t,a)}else a=s.get(t),a===void 0&&(a=new Set,s.set(t,a));a.has(i)||(a.add(i),e=py.bind(null,e,t,i),t.then(e,e))}function Ef(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Af(e,t,i,s,a){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,i.flags|=131072,i.flags&=-52805,i.tag===1&&(i.alternate===null?i.tag=17:(t=Ht(-1,1),t.tag=2,cn(i,t,1))),i.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var ey=q.ReactCurrentOwner,it=!1;function Je(e,t,i,s){t.child=e===null?Kc(t,null,i,s):or(t,e.child,i,s)}function Mf(e,t,i,s,a){i=i.render;var c=t.ref;return lr(t,a),s=vl(e,t,i,s,c,a),i=xl(),e!==null&&!it?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Kt(e,t,a)):(Te&&i&&tl(t),t.flags|=1,Je(e,t,s,a),t.child)}function Rf(e,t,i,s,a){if(e===null){var c=i.type;return typeof c=="function"&&!Yl(c)&&c.defaultProps===void 0&&i.compare===null&&i.defaultProps===void 0?(t.tag=15,t.type=c,Nf(e,t,c,s,a)):(e=_o(i.type,null,s,t,t.mode,a),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,(e.lanes&a)===0){var h=c.memoizedProps;if(i=i.compare,i=i!==null?i:$r,i(h,s)&&e.ref===t.ref)return Kt(e,t,a)}return t.flags|=1,e=gn(c,s),e.ref=t.ref,e.return=t,t.child=e}function Nf(e,t,i,s,a){if(e!==null){var c=e.memoizedProps;if($r(c,s)&&e.ref===t.ref)if(it=!1,t.pendingProps=s=c,(e.lanes&a)!==0)(e.flags&131072)!==0&&(it=!0);else return t.lanes=e.lanes,Kt(e,t,a)}return Ml(e,t,i,s,a)}function Df(e,t,i){var s=t.pendingProps,a=s.children,c=e!==null?e.memoizedState:null;if(s.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},xe(fr,dt),dt|=i;else{if((i&1073741824)===0)return e=c!==null?c.baseLanes|i:i,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,xe(fr,dt),dt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=c!==null?c.baseLanes:i,xe(fr,dt),dt|=s}else c!==null?(s=c.baseLanes|i,t.memoizedState=null):s=i,xe(fr,dt),dt|=s;return Je(e,t,a,i),t.child}function Vf(e,t){var i=t.ref;(e===null&&i!==null||e!==null&&e.ref!==i)&&(t.flags|=512,t.flags|=2097152)}function Ml(e,t,i,s,a){var c=rt(i)?En:Ge.current;return c=tr(t,c),lr(t,a),i=vl(e,t,i,s,c,a),s=xl(),e!==null&&!it?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Kt(e,t,a)):(Te&&s&&tl(t),t.flags|=1,Je(e,t,i,a),t.child)}function Lf(e,t,i,s,a){if(rt(i)){var c=!0;io(t)}else c=!1;if(lr(t,a),t.stateNode===null)Co(e,t),Sf(t,i,s),Tl(t,i,s,a),s=!0;else if(e===null){var h=t.stateNode,v=t.memoizedProps;h.props=v;var w=h.context,E=i.contextType;typeof E=="object"&&E!==null?E=vt(E):(E=rt(i)?En:Ge.current,E=tr(t,E));var L=i.getDerivedStateFromProps,j=typeof L=="function"||typeof h.getSnapshotBeforeUpdate=="function";j||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==s||w!==E)&&kf(t,h,s,E),un=!1;var D=t.memoizedState;h.state=D,ho(t,s,h,a),w=t.memoizedState,v!==s||D!==w||nt.current||un?(typeof L=="function"&&(Pl(t,i,L,s),w=t.memoizedState),(v=un||wf(t,i,v,s,D,w,E))?(j||typeof h.UNSAFE_componentWillMount!="function"&&typeof h.componentWillMount!="function"||(typeof h.componentWillMount=="function"&&h.componentWillMount(),typeof h.UNSAFE_componentWillMount=="function"&&h.UNSAFE_componentWillMount()),typeof h.componentDidMount=="function"&&(t.flags|=4194308)):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=w),h.props=s,h.state=w,h.context=E,s=v):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{h=t.stateNode,Qc(e,t),v=t.memoizedProps,E=t.type===t.elementType?v:Mt(t.type,v),h.props=E,j=t.pendingProps,D=h.context,w=i.contextType,typeof w=="object"&&w!==null?w=vt(w):(w=rt(i)?En:Ge.current,w=tr(t,w));var $=i.getDerivedStateFromProps;(L=typeof $=="function"||typeof h.getSnapshotBeforeUpdate=="function")||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==j||D!==w)&&kf(t,h,s,w),un=!1,D=t.memoizedState,h.state=D,ho(t,s,h,a);var K=t.memoizedState;v!==j||D!==K||nt.current||un?(typeof $=="function"&&(Pl(t,i,$,s),K=t.memoizedState),(E=un||wf(t,i,E,s,D,K,w)||!1)?(L||typeof h.UNSAFE_componentWillUpdate!="function"&&typeof h.componentWillUpdate!="function"||(typeof h.componentWillUpdate=="function"&&h.componentWillUpdate(s,K,w),typeof h.UNSAFE_componentWillUpdate=="function"&&h.UNSAFE_componentWillUpdate(s,K,w)),typeof h.componentDidUpdate=="function"&&(t.flags|=4),typeof h.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=K),h.props=s,h.state=K,h.context=w,s=E):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),s=!1)}return Rl(e,t,i,s,c,a)}function Rl(e,t,i,s,a,c){Vf(e,t);var h=(t.flags&128)!==0;if(!s&&!h)return a&&Oc(t,i,!1),Kt(e,t,c);s=t.stateNode,ey.current=t;var v=h&&typeof i.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&h?(t.child=or(t,e.child,null,c),t.child=or(t,null,v,c)):Je(e,t,v,c),t.memoizedState=s.state,a&&Oc(t,i,!0),t.child}function _f(e){var t=e.stateNode;t.pendingContext?jc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&jc(e,t.context,!1),dl(e,t.containerInfo)}function jf(e,t,i,s,a){return ir(),ol(a),t.flags|=256,Je(e,t,i,s),t.child}var Nl={dehydrated:null,treeContext:null,retryLane:0};function Dl(e){return{baseLanes:e,cachePool:null,transitions:null}}function If(e,t,i){var s=t.pendingProps,a=Ee.current,c=!1,h=(t.flags&128)!==0,v;if((v=h)||(v=e!==null&&e.memoizedState===null?!1:(a&2)!==0),v?(c=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(a|=1),xe(Ee,a&1),e===null)return il(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(h=s.children,e=s.fallback,c?(s=t.mode,c=t.child,h={mode:"hidden",children:h},(s&1)===0&&c!==null?(c.childLanes=0,c.pendingProps=h):c=jo(h,s,0,null),e=In(e,s,i,null),c.return=t,e.return=t,c.sibling=e,t.child=c,t.child.memoizedState=Dl(i),t.memoizedState=Nl,e):Vl(t,h));if(a=e.memoizedState,a!==null&&(v=a.dehydrated,v!==null))return ty(e,t,h,s,v,a,i);if(c){c=s.fallback,h=t.mode,a=e.child,v=a.sibling;var w={mode:"hidden",children:s.children};return(h&1)===0&&t.child!==a?(s=t.child,s.childLanes=0,s.pendingProps=w,t.deletions=null):(s=gn(a,w),s.subtreeFlags=a.subtreeFlags&14680064),v!==null?c=gn(v,c):(c=In(c,h,i,null),c.flags|=2),c.return=t,s.return=t,s.sibling=c,t.child=s,s=c,c=t.child,h=e.child.memoizedState,h=h===null?Dl(i):{baseLanes:h.baseLanes|i,cachePool:null,transitions:h.transitions},c.memoizedState=h,c.childLanes=e.childLanes&~i,t.memoizedState=Nl,s}return c=e.child,e=c.sibling,s=gn(c,{mode:"visible",children:s.children}),(t.mode&1)===0&&(s.lanes=i),s.return=t,s.sibling=null,e!==null&&(i=t.deletions,i===null?(t.deletions=[e],t.flags|=16):i.push(e)),t.child=s,t.memoizedState=null,s}function Vl(e,t){return t=jo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ko(e,t,i,s){return s!==null&&ol(s),or(t,e.child,null,i),e=Vl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ty(e,t,i,s,a,c,h){if(i)return t.flags&256?(t.flags&=-257,s=El(Error(o(422))),ko(e,t,h,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(c=s.fallback,a=t.mode,s=jo({mode:"visible",children:s.children},a,0,null),c=In(c,a,h,null),c.flags|=2,s.return=t,c.return=t,s.sibling=c,t.child=s,(t.mode&1)!==0&&or(t,e.child,null,h),t.child.memoizedState=Dl(h),t.memoizedState=Nl,c);if((t.mode&1)===0)return ko(e,t,h,null);if(a.data==="$!"){if(s=a.nextSibling&&a.nextSibling.dataset,s)var v=s.dgst;return s=v,c=Error(o(419)),s=El(c,s,void 0),ko(e,t,h,s)}if(v=(h&e.childLanes)!==0,it||v){if(s=Ue,s!==null){switch(h&-h){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}a=(a&(s.suspendedLanes|h))!==0?0:a,a!==0&&a!==c.retryLane&&(c.retryLane=a,$t(e,a),Dt(s,e,a,-1))}return Ql(),s=El(Error(o(421))),ko(e,t,h,s)}return a.data==="$?"?(t.flags|=128,t.child=e.child,t=hy.bind(null,e),a._reactRetry=t,null):(e=c.treeContext,ft=on(a.nextSibling),ct=t,Te=!0,At=null,e!==null&&(gt[yt++]=Ut,gt[yt++]=Wt,gt[yt++]=An,Ut=e.id,Wt=e.overflow,An=t),t=Vl(t,s.children),t.flags|=4096,t)}function Of(e,t,i){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),ul(e.return,t,i)}function Ll(e,t,i,s,a){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:i,tailMode:a}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=s,c.tail=i,c.tailMode=a)}function zf(e,t,i){var s=t.pendingProps,a=s.revealOrder,c=s.tail;if(Je(e,t,s.children,i),s=Ee.current,(s&2)!==0)s=s&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Of(e,i,t);else if(e.tag===19)Of(e,i,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(xe(Ee,s),(t.mode&1)===0)t.memoizedState=null;else switch(a){case"forwards":for(i=t.child,a=null;i!==null;)e=i.alternate,e!==null&&mo(e)===null&&(a=i),i=i.sibling;i=a,i===null?(a=t.child,t.child=null):(a=i.sibling,i.sibling=null),Ll(t,!1,a,i,c);break;case"backwards":for(i=null,a=t.child,t.child=null;a!==null;){if(e=a.alternate,e!==null&&mo(e)===null){t.child=a;break}e=a.sibling,a.sibling=i,i=a,a=e}Ll(t,!0,i,null,c);break;case"together":Ll(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Co(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Kt(e,t,i){if(e!==null&&(t.dependencies=e.dependencies),Vn|=t.lanes,(i&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,i=gn(e,e.pendingProps),t.child=i,i.return=t;e.sibling!==null;)e=e.sibling,i=i.sibling=gn(e,e.pendingProps),i.return=t;i.sibling=null}return t.child}function ny(e,t,i){switch(t.tag){case 3:_f(t),ir();break;case 5:Zc(t);break;case 1:rt(t.type)&&io(t);break;case 4:dl(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,a=t.memoizedProps.value;xe(co,s._currentValue),s._currentValue=a;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(xe(Ee,Ee.current&1),t.flags|=128,null):(i&t.child.childLanes)!==0?If(e,t,i):(xe(Ee,Ee.current&1),e=Kt(e,t,i),e!==null?e.sibling:null);xe(Ee,Ee.current&1);break;case 19:if(s=(i&t.childLanes)!==0,(e.flags&128)!==0){if(s)return zf(e,t,i);t.flags|=128}if(a=t.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),xe(Ee,Ee.current),s)break;return null;case 22:case 23:return t.lanes=0,Df(e,t,i)}return Kt(e,t,i)}var Ff,_l,bf,Bf;Ff=function(e,t){for(var i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break;for(;i.sibling===null;){if(i.return===null||i.return===t)return;i=i.return}i.sibling.return=i.return,i=i.sibling}},_l=function(){},bf=function(e,t,i,s){var a=e.memoizedProps;if(a!==s){e=t.stateNode,Nn(jt.current);var c=null;switch(i){case"input":a=us(e,a),s=us(e,s),c=[];break;case"select":a=B({},a,{value:void 0}),s=B({},s,{value:void 0}),c=[];break;case"textarea":a=ds(e,a),s=ds(e,s),c=[];break;default:typeof a.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=to)}hs(i,s);var h;i=null;for(E in a)if(!s.hasOwnProperty(E)&&a.hasOwnProperty(E)&&a[E]!=null)if(E==="style"){var v=a[E];for(h in v)v.hasOwnProperty(h)&&(i||(i={}),i[h]="")}else E!=="dangerouslySetInnerHTML"&&E!=="children"&&E!=="suppressContentEditableWarning"&&E!=="suppressHydrationWarning"&&E!=="autoFocus"&&(u.hasOwnProperty(E)?c||(c=[]):(c=c||[]).push(E,null));for(E in s){var w=s[E];if(v=a!=null?a[E]:void 0,s.hasOwnProperty(E)&&w!==v&&(w!=null||v!=null))if(E==="style")if(v){for(h in v)!v.hasOwnProperty(h)||w&&w.hasOwnProperty(h)||(i||(i={}),i[h]="");for(h in w)w.hasOwnProperty(h)&&v[h]!==w[h]&&(i||(i={}),i[h]=w[h])}else i||(c||(c=[]),c.push(E,i)),i=w;else E==="dangerouslySetInnerHTML"?(w=w?w.__html:void 0,v=v?v.__html:void 0,w!=null&&v!==w&&(c=c||[]).push(E,w)):E==="children"?typeof w!="string"&&typeof w!="number"||(c=c||[]).push(E,""+w):E!=="suppressContentEditableWarning"&&E!=="suppressHydrationWarning"&&(u.hasOwnProperty(E)?(w!=null&&E==="onScroll"&&we("scroll",e),c||v===w||(c=[])):(c=c||[]).push(E,w))}i&&(c=c||[]).push("style",i);var E=c;(t.updateQueue=E)&&(t.flags|=4)}},Bf=function(e,t,i,s){i!==s&&(t.flags|=4)};function oi(e,t){if(!Te)switch(e.tailMode){case"hidden":t=e.tail;for(var i=null;t!==null;)t.alternate!==null&&(i=t),t=t.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var s=null;i!==null;)i.alternate!==null&&(s=i),i=i.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function Ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,i=0,s=0;if(t)for(var a=e.child;a!==null;)i|=a.lanes|a.childLanes,s|=a.subtreeFlags&14680064,s|=a.flags&14680064,a.return=e,a=a.sibling;else for(a=e.child;a!==null;)i|=a.lanes|a.childLanes,s|=a.subtreeFlags,s|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=s,e.childLanes=i,t}function ry(e,t,i){var s=t.pendingProps;switch(nl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ye(t),null;case 1:return rt(t.type)&&ro(),Ye(t),null;case 3:return s=t.stateNode,ar(),Se(nt),Se(Ge),ml(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(ao(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,At!==null&&(Hl(At),At=null))),_l(e,t),Ye(t),null;case 5:pl(t);var a=Nn(ei.current);if(i=t.type,e!==null&&t.stateNode!=null)bf(e,t,i,s,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(o(166));return Ye(t),null}if(e=Nn(jt.current),ao(t)){s=t.stateNode,i=t.type;var c=t.memoizedProps;switch(s[_t]=t,s[Yr]=c,e=(t.mode&1)!==0,i){case"dialog":we("cancel",s),we("close",s);break;case"iframe":case"object":case"embed":we("load",s);break;case"video":case"audio":for(a=0;a<Kr.length;a++)we(Kr[a],s);break;case"source":we("error",s);break;case"img":case"image":case"link":we("error",s),we("load",s);break;case"details":we("toggle",s);break;case"input":wu(s,c),we("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!c.multiple},we("invalid",s);break;case"textarea":Cu(s,c),we("invalid",s)}hs(i,c),a=null;for(var h in c)if(c.hasOwnProperty(h)){var v=c[h];h==="children"?typeof v=="string"?s.textContent!==v&&(c.suppressHydrationWarning!==!0&&eo(s.textContent,v,e),a=["children",v]):typeof v=="number"&&s.textContent!==""+v&&(c.suppressHydrationWarning!==!0&&eo(s.textContent,v,e),a=["children",""+v]):u.hasOwnProperty(h)&&v!=null&&h==="onScroll"&&we("scroll",s)}switch(i){case"input":Di(s),ku(s,c,!0);break;case"textarea":Di(s),Tu(s);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(s.onclick=to)}s=a,t.updateQueue=s,s!==null&&(t.flags|=4)}else{h=a.nodeType===9?a:a.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Eu(i)),e==="http://www.w3.org/1999/xhtml"?i==="script"?(e=h.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=h.createElement(i,{is:s.is}):(e=h.createElement(i),i==="select"&&(h=e,s.multiple?h.multiple=!0:s.size&&(h.size=s.size))):e=h.createElementNS(e,i),e[_t]=t,e[Yr]=s,Ff(e,t,!1,!1),t.stateNode=e;e:{switch(h=ms(i,s),i){case"dialog":we("cancel",e),we("close",e),a=s;break;case"iframe":case"object":case"embed":we("load",e),a=s;break;case"video":case"audio":for(a=0;a<Kr.length;a++)we(Kr[a],e);a=s;break;case"source":we("error",e),a=s;break;case"img":case"image":case"link":we("error",e),we("load",e),a=s;break;case"details":we("toggle",e),a=s;break;case"input":wu(e,s),a=us(e,s),we("invalid",e);break;case"option":a=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},a=B({},s,{value:void 0}),we("invalid",e);break;case"textarea":Cu(e,s),a=ds(e,s),we("invalid",e);break;default:a=s}hs(i,a),v=a;for(c in v)if(v.hasOwnProperty(c)){var w=v[c];c==="style"?Ru(e,w):c==="dangerouslySetInnerHTML"?(w=w?w.__html:void 0,w!=null&&Au(e,w)):c==="children"?typeof w=="string"?(i!=="textarea"||w!=="")&&Ar(e,w):typeof w=="number"&&Ar(e,""+w):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(u.hasOwnProperty(c)?w!=null&&c==="onScroll"&&we("scroll",e):w!=null&&U(e,c,w,h))}switch(i){case"input":Di(e),ku(e,s,!1);break;case"textarea":Di(e),Tu(e);break;case"option":s.value!=null&&e.setAttribute("value",""+he(s.value));break;case"select":e.multiple=!!s.multiple,c=s.value,c!=null?Wn(e,!!s.multiple,c,!1):s.defaultValue!=null&&Wn(e,!!s.multiple,s.defaultValue,!0);break;default:typeof a.onClick=="function"&&(e.onclick=to)}switch(i){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ye(t),null;case 6:if(e&&t.stateNode!=null)Bf(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(o(166));if(i=Nn(ei.current),Nn(jt.current),ao(t)){if(s=t.stateNode,i=t.memoizedProps,s[_t]=t,(c=s.nodeValue!==i)&&(e=ct,e!==null))switch(e.tag){case 3:eo(s.nodeValue,i,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&eo(s.nodeValue,i,(e.mode&1)!==0)}c&&(t.flags|=4)}else s=(i.nodeType===9?i:i.ownerDocument).createTextNode(s),s[_t]=t,t.stateNode=s}return Ye(t),null;case 13:if(Se(Ee),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Te&&ft!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Wc(),ir(),t.flags|=98560,c=!1;else if(c=ao(t),s!==null&&s.dehydrated!==null){if(e===null){if(!c)throw Error(o(318));if(c=t.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(o(317));c[_t]=t}else ir(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ye(t),c=!1}else At!==null&&(Hl(At),At=null),c=!0;if(!c)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=i,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Ee.current&1)!==0?ze===0&&(ze=3):Ql())),t.updateQueue!==null&&(t.flags|=4),Ye(t),null);case 4:return ar(),_l(e,t),e===null&&Gr(t.stateNode.containerInfo),Ye(t),null;case 10:return al(t.type._context),Ye(t),null;case 17:return rt(t.type)&&ro(),Ye(t),null;case 19:if(Se(Ee),c=t.memoizedState,c===null)return Ye(t),null;if(s=(t.flags&128)!==0,h=c.rendering,h===null)if(s)oi(c,!1);else{if(ze!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(h=mo(e),h!==null){for(t.flags|=128,oi(c,!1),s=h.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=i,i=t.child;i!==null;)c=i,e=s,c.flags&=14680066,h=c.alternate,h===null?(c.childLanes=0,c.lanes=e,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=h.childLanes,c.lanes=h.lanes,c.child=h.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=h.memoizedProps,c.memoizedState=h.memoizedState,c.updateQueue=h.updateQueue,c.type=h.type,e=h.dependencies,c.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),i=i.sibling;return xe(Ee,Ee.current&1|2),t.child}e=e.sibling}c.tail!==null&&De()>dr&&(t.flags|=128,s=!0,oi(c,!1),t.lanes=4194304)}else{if(!s)if(e=mo(h),e!==null){if(t.flags|=128,s=!0,i=e.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),oi(c,!0),c.tail===null&&c.tailMode==="hidden"&&!h.alternate&&!Te)return Ye(t),null}else 2*De()-c.renderingStartTime>dr&&i!==1073741824&&(t.flags|=128,s=!0,oi(c,!1),t.lanes=4194304);c.isBackwards?(h.sibling=t.child,t.child=h):(i=c.last,i!==null?i.sibling=h:t.child=h,c.last=h)}return c.tail!==null?(t=c.tail,c.rendering=t,c.tail=t.sibling,c.renderingStartTime=De(),t.sibling=null,i=Ee.current,xe(Ee,s?i&1|2:i&1),t):(Ye(t),null);case 22:case 23:return Gl(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&(t.mode&1)!==0?(dt&1073741824)!==0&&(Ye(t),t.subtreeFlags&6&&(t.flags|=8192)):Ye(t),null;case 24:return null;case 25:return null}throw Error(o(156,t.tag))}function iy(e,t){switch(nl(t),t.tag){case 1:return rt(t.type)&&ro(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ar(),Se(nt),Se(Ge),ml(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return pl(t),null;case 13:if(Se(Ee),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));ir()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Se(Ee),null;case 4:return ar(),null;case 10:return al(t.type._context),null;case 22:case 23:return Gl(),null;case 24:return null;default:return null}}var Po=!1,Xe=!1,oy=typeof WeakSet=="function"?WeakSet:Set,H=null;function cr(e,t){var i=e.ref;if(i!==null)if(typeof i=="function")try{i(null)}catch(s){Re(e,t,s)}else i.current=null}function jl(e,t,i){try{i()}catch(s){Re(e,t,s)}}var Uf=!1;function sy(e,t){if(Gs=Wi,e=xc(),Fs(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var s=i.getSelection&&i.getSelection();if(s&&s.rangeCount!==0){i=s.anchorNode;var a=s.anchorOffset,c=s.focusNode;s=s.focusOffset;try{i.nodeType,c.nodeType}catch{i=null;break e}var h=0,v=-1,w=-1,E=0,L=0,j=e,D=null;t:for(;;){for(var $;j!==i||a!==0&&j.nodeType!==3||(v=h+a),j!==c||s!==0&&j.nodeType!==3||(w=h+s),j.nodeType===3&&(h+=j.nodeValue.length),($=j.firstChild)!==null;)D=j,j=$;for(;;){if(j===e)break t;if(D===i&&++E===a&&(v=h),D===c&&++L===s&&(w=h),($=j.nextSibling)!==null)break;j=D,D=j.parentNode}j=$}i=v===-1||w===-1?null:{start:v,end:w}}else i=null}i=i||{start:0,end:0}}else i=null;for(Qs={focusedElem:e,selectionRange:i},Wi=!1,H=t;H!==null;)if(t=H,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,H=e;else for(;H!==null;){t=H;try{var K=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(K!==null){var Q=K.memoizedProps,Ve=K.memoizedState,P=t.stateNode,k=P.getSnapshotBeforeUpdate(t.elementType===t.type?Q:Mt(t.type,Q),Ve);P.__reactInternalSnapshotBeforeUpdate=k}break;case 3:var T=t.stateNode.containerInfo;T.nodeType===1?T.textContent="":T.nodeType===9&&T.documentElement&&T.removeChild(T.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(o(163))}}catch(O){Re(t,t.return,O)}if(e=t.sibling,e!==null){e.return=t.return,H=e;break}H=t.return}return K=Uf,Uf=!1,K}function si(e,t,i){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var a=s=s.next;do{if((a.tag&e)===e){var c=a.destroy;a.destroy=void 0,c!==void 0&&jl(t,i,c)}a=a.next}while(a!==s)}}function To(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var i=t=t.next;do{if((i.tag&e)===e){var s=i.create;i.destroy=s()}i=i.next}while(i!==t)}}function Il(e){var t=e.ref;if(t!==null){var i=e.stateNode;switch(e.tag){case 5:e=i;break;default:e=i}typeof t=="function"?t(e):t.current=e}}function Wf(e){var t=e.alternate;t!==null&&(e.alternate=null,Wf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[_t],delete t[Yr],delete t[qs],delete t[Ug],delete t[Wg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function $f(e){return e.tag===5||e.tag===3||e.tag===4}function Hf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||$f(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ol(e,t,i){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?i.nodeType===8?i.parentNode.insertBefore(e,t):i.insertBefore(e,t):(i.nodeType===8?(t=i.parentNode,t.insertBefore(e,i)):(t=i,t.appendChild(e)),i=i._reactRootContainer,i!=null||t.onclick!==null||(t.onclick=to));else if(s!==4&&(e=e.child,e!==null))for(Ol(e,t,i),e=e.sibling;e!==null;)Ol(e,t,i),e=e.sibling}function zl(e,t,i){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?i.insertBefore(e,t):i.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(zl(e,t,i),e=e.sibling;e!==null;)zl(e,t,i),e=e.sibling}var $e=null,Rt=!1;function fn(e,t,i){for(i=i.child;i!==null;)Kf(e,t,i),i=i.sibling}function Kf(e,t,i){if(Lt&&typeof Lt.onCommitFiberUnmount=="function")try{Lt.onCommitFiberUnmount(Oi,i)}catch{}switch(i.tag){case 5:Xe||cr(i,t);case 6:var s=$e,a=Rt;$e=null,fn(e,t,i),$e=s,Rt=a,$e!==null&&(Rt?(e=$e,i=i.stateNode,e.nodeType===8?e.parentNode.removeChild(i):e.removeChild(i)):$e.removeChild(i.stateNode));break;case 18:$e!==null&&(Rt?(e=$e,i=i.stateNode,e.nodeType===8?Zs(e.parentNode,i):e.nodeType===1&&Zs(e,i),zr(e)):Zs($e,i.stateNode));break;case 4:s=$e,a=Rt,$e=i.stateNode.containerInfo,Rt=!0,fn(e,t,i),$e=s,Rt=a;break;case 0:case 11:case 14:case 15:if(!Xe&&(s=i.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){a=s=s.next;do{var c=a,h=c.destroy;c=c.tag,h!==void 0&&((c&2)!==0||(c&4)!==0)&&jl(i,t,h),a=a.next}while(a!==s)}fn(e,t,i);break;case 1:if(!Xe&&(cr(i,t),s=i.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=i.memoizedProps,s.state=i.memoizedState,s.componentWillUnmount()}catch(v){Re(i,t,v)}fn(e,t,i);break;case 21:fn(e,t,i);break;case 22:i.mode&1?(Xe=(s=Xe)||i.memoizedState!==null,fn(e,t,i),Xe=s):fn(e,t,i);break;default:fn(e,t,i)}}function Gf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var i=e.stateNode;i===null&&(i=e.stateNode=new oy),t.forEach(function(s){var a=my.bind(null,e,s);i.has(s)||(i.add(s),s.then(a,a))})}}function Nt(e,t){var i=t.deletions;if(i!==null)for(var s=0;s<i.length;s++){var a=i[s];try{var c=e,h=t,v=h;e:for(;v!==null;){switch(v.tag){case 5:$e=v.stateNode,Rt=!1;break e;case 3:$e=v.stateNode.containerInfo,Rt=!0;break e;case 4:$e=v.stateNode.containerInfo,Rt=!0;break e}v=v.return}if($e===null)throw Error(o(160));Kf(c,h,a),$e=null,Rt=!1;var w=a.alternate;w!==null&&(w.return=null),a.return=null}catch(E){Re(a,t,E)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Qf(t,e),t=t.sibling}function Qf(e,t){var i=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Nt(t,e),Ot(e),s&4){try{si(3,e,e.return),To(3,e)}catch(Q){Re(e,e.return,Q)}try{si(5,e,e.return)}catch(Q){Re(e,e.return,Q)}}break;case 1:Nt(t,e),Ot(e),s&512&&i!==null&&cr(i,i.return);break;case 5:if(Nt(t,e),Ot(e),s&512&&i!==null&&cr(i,i.return),e.flags&32){var a=e.stateNode;try{Ar(a,"")}catch(Q){Re(e,e.return,Q)}}if(s&4&&(a=e.stateNode,a!=null)){var c=e.memoizedProps,h=i!==null?i.memoizedProps:c,v=e.type,w=e.updateQueue;if(e.updateQueue=null,w!==null)try{v==="input"&&c.type==="radio"&&c.name!=null&&Su(a,c),ms(v,h);var E=ms(v,c);for(h=0;h<w.length;h+=2){var L=w[h],j=w[h+1];L==="style"?Ru(a,j):L==="dangerouslySetInnerHTML"?Au(a,j):L==="children"?Ar(a,j):U(a,L,j,E)}switch(v){case"input":cs(a,c);break;case"textarea":Pu(a,c);break;case"select":var D=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!c.multiple;var $=c.value;$!=null?Wn(a,!!c.multiple,$,!1):D!==!!c.multiple&&(c.defaultValue!=null?Wn(a,!!c.multiple,c.defaultValue,!0):Wn(a,!!c.multiple,c.multiple?[]:"",!1))}a[Yr]=c}catch(Q){Re(e,e.return,Q)}}break;case 6:if(Nt(t,e),Ot(e),s&4){if(e.stateNode===null)throw Error(o(162));a=e.stateNode,c=e.memoizedProps;try{a.nodeValue=c}catch(Q){Re(e,e.return,Q)}}break;case 3:if(Nt(t,e),Ot(e),s&4&&i!==null&&i.memoizedState.isDehydrated)try{zr(t.containerInfo)}catch(Q){Re(e,e.return,Q)}break;case 4:Nt(t,e),Ot(e);break;case 13:Nt(t,e),Ot(e),a=e.child,a.flags&8192&&(c=a.memoizedState!==null,a.stateNode.isHidden=c,!c||a.alternate!==null&&a.alternate.memoizedState!==null||(Bl=De())),s&4&&Gf(e);break;case 22:if(L=i!==null&&i.memoizedState!==null,e.mode&1?(Xe=(E=Xe)||L,Nt(t,e),Xe=E):Nt(t,e),Ot(e),s&8192){if(E=e.memoizedState!==null,(e.stateNode.isHidden=E)&&!L&&(e.mode&1)!==0)for(H=e,L=e.child;L!==null;){for(j=H=L;H!==null;){switch(D=H,$=D.child,D.tag){case 0:case 11:case 14:case 15:si(4,D,D.return);break;case 1:cr(D,D.return);var K=D.stateNode;if(typeof K.componentWillUnmount=="function"){s=D,i=D.return;try{t=s,K.props=t.memoizedProps,K.state=t.memoizedState,K.componentWillUnmount()}catch(Q){Re(s,i,Q)}}break;case 5:cr(D,D.return);break;case 22:if(D.memoizedState!==null){Zf(j);continue}}$!==null?($.return=D,H=$):Zf(j)}L=L.sibling}e:for(L=null,j=e;;){if(j.tag===5){if(L===null){L=j;try{a=j.stateNode,E?(c=a.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(v=j.stateNode,w=j.memoizedProps.style,h=w!=null&&w.hasOwnProperty("display")?w.display:null,v.style.display=Mu("display",h))}catch(Q){Re(e,e.return,Q)}}}else if(j.tag===6){if(L===null)try{j.stateNode.nodeValue=E?"":j.memoizedProps}catch(Q){Re(e,e.return,Q)}}else if((j.tag!==22&&j.tag!==23||j.memoizedState===null||j===e)&&j.child!==null){j.child.return=j,j=j.child;continue}if(j===e)break e;for(;j.sibling===null;){if(j.return===null||j.return===e)break e;L===j&&(L=null),j=j.return}L===j&&(L=null),j.sibling.return=j.return,j=j.sibling}}break;case 19:Nt(t,e),Ot(e),s&4&&Gf(e);break;case 21:break;default:Nt(t,e),Ot(e)}}function Ot(e){var t=e.flags;if(t&2){try{e:{for(var i=e.return;i!==null;){if($f(i)){var s=i;break e}i=i.return}throw Error(o(160))}switch(s.tag){case 5:var a=s.stateNode;s.flags&32&&(Ar(a,""),s.flags&=-33);var c=Hf(e);zl(e,c,a);break;case 3:case 4:var h=s.stateNode.containerInfo,v=Hf(e);Ol(e,v,h);break;default:throw Error(o(161))}}catch(w){Re(e,e.return,w)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ly(e,t,i){H=e,Yf(e)}function Yf(e,t,i){for(var s=(e.mode&1)!==0;H!==null;){var a=H,c=a.child;if(a.tag===22&&s){var h=a.memoizedState!==null||Po;if(!h){var v=a.alternate,w=v!==null&&v.memoizedState!==null||Xe;v=Po;var E=Xe;if(Po=h,(Xe=w)&&!E)for(H=a;H!==null;)h=H,w=h.child,h.tag===22&&h.memoizedState!==null?qf(a):w!==null?(w.return=h,H=w):qf(a);for(;c!==null;)H=c,Yf(c),c=c.sibling;H=a,Po=v,Xe=E}Xf(e)}else(a.subtreeFlags&8772)!==0&&c!==null?(c.return=a,H=c):Xf(e)}}function Xf(e){for(;H!==null;){var t=H;if((t.flags&8772)!==0){var i=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Xe||To(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!Xe)if(i===null)s.componentDidMount();else{var a=t.elementType===t.type?i.memoizedProps:Mt(t.type,i.memoizedProps);s.componentDidUpdate(a,i.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var c=t.updateQueue;c!==null&&Xc(t,c,s);break;case 3:var h=t.updateQueue;if(h!==null){if(i=null,t.child!==null)switch(t.child.tag){case 5:i=t.child.stateNode;break;case 1:i=t.child.stateNode}Xc(t,h,i)}break;case 5:var v=t.stateNode;if(i===null&&t.flags&4){i=v;var w=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":w.autoFocus&&i.focus();break;case"img":w.src&&(i.src=w.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var E=t.alternate;if(E!==null){var L=E.memoizedState;if(L!==null){var j=L.dehydrated;j!==null&&zr(j)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(o(163))}Xe||t.flags&512&&Il(t)}catch(D){Re(t,t.return,D)}}if(t===e){H=null;break}if(i=t.sibling,i!==null){i.return=t.return,H=i;break}H=t.return}}function Zf(e){for(;H!==null;){var t=H;if(t===e){H=null;break}var i=t.sibling;if(i!==null){i.return=t.return,H=i;break}H=t.return}}function qf(e){for(;H!==null;){var t=H;try{switch(t.tag){case 0:case 11:case 15:var i=t.return;try{To(4,t)}catch(w){Re(t,i,w)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var a=t.return;try{s.componentDidMount()}catch(w){Re(t,a,w)}}var c=t.return;try{Il(t)}catch(w){Re(t,c,w)}break;case 5:var h=t.return;try{Il(t)}catch(w){Re(t,h,w)}}}catch(w){Re(t,t.return,w)}if(t===e){H=null;break}var v=t.sibling;if(v!==null){v.return=t.return,H=v;break}H=t.return}}var ay=Math.ceil,Eo=q.ReactCurrentDispatcher,Fl=q.ReactCurrentOwner,wt=q.ReactCurrentBatchConfig,ce=0,Ue=null,je=null,He=0,dt=0,fr=sn(0),ze=0,li=null,Vn=0,Ao=0,bl=0,ai=null,ot=null,Bl=0,dr=1/0,Gt=null,Mo=!1,Ul=null,dn=null,Ro=!1,pn=null,No=0,ui=0,Wl=null,Do=-1,Vo=0;function et(){return(ce&6)!==0?De():Do!==-1?Do:Do=De()}function hn(e){return(e.mode&1)===0?1:(ce&2)!==0&&He!==0?He&-He:Hg.transition!==null?(Vo===0&&(Vo=$u()),Vo):(e=me,e!==0||(e=window.event,e=e===void 0?16:Ju(e.type)),e)}function Dt(e,t,i,s){if(50<ui)throw ui=0,Wl=null,Error(o(185));Lr(e,i,s),((ce&2)===0||e!==Ue)&&(e===Ue&&((ce&2)===0&&(Ao|=i),ze===4&&mn(e,He)),st(e,s),i===1&&ce===0&&(t.mode&1)===0&&(dr=De()+500,oo&&an()))}function st(e,t){var i=e.callbackNode;Hm(e,t);var s=bi(e,e===Ue?He:0);if(s===0)i!==null&&Bu(i),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(i!=null&&Bu(i),t===1)e.tag===0?$g(ed.bind(null,e)):zc(ed.bind(null,e)),bg(function(){(ce&6)===0&&an()}),i=null;else{switch(Hu(s)){case 1:i=ks;break;case 4:i=Uu;break;case 16:i=Ii;break;case 536870912:i=Wu;break;default:i=Ii}i=ad(i,Jf.bind(null,e))}e.callbackPriority=t,e.callbackNode=i}}function Jf(e,t){if(Do=-1,Vo=0,(ce&6)!==0)throw Error(o(327));var i=e.callbackNode;if(pr()&&e.callbackNode!==i)return null;var s=bi(e,e===Ue?He:0);if(s===0)return null;if((s&30)!==0||(s&e.expiredLanes)!==0||t)t=Lo(e,s);else{t=s;var a=ce;ce|=2;var c=nd();(Ue!==e||He!==t)&&(Gt=null,dr=De()+500,_n(e,t));do try{fy();break}catch(v){td(e,v)}while(!0);ll(),Eo.current=c,ce=a,je!==null?t=0:(Ue=null,He=0,t=ze)}if(t!==0){if(t===2&&(a=Cs(e),a!==0&&(s=a,t=$l(e,a))),t===1)throw i=li,_n(e,0),mn(e,s),st(e,De()),i;if(t===6)mn(e,s);else{if(a=e.current.alternate,(s&30)===0&&!uy(a)&&(t=Lo(e,s),t===2&&(c=Cs(e),c!==0&&(s=c,t=$l(e,c))),t===1))throw i=li,_n(e,0),mn(e,s),st(e,De()),i;switch(e.finishedWork=a,e.finishedLanes=s,t){case 0:case 1:throw Error(o(345));case 2:jn(e,ot,Gt);break;case 3:if(mn(e,s),(s&130023424)===s&&(t=Bl+500-De(),10<t)){if(bi(e,0)!==0)break;if(a=e.suspendedLanes,(a&s)!==s){et(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=Xs(jn.bind(null,e,ot,Gt),t);break}jn(e,ot,Gt);break;case 4:if(mn(e,s),(s&4194240)===s)break;for(t=e.eventTimes,a=-1;0<s;){var h=31-Tt(s);c=1<<h,h=t[h],h>a&&(a=h),s&=~c}if(s=a,s=De()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*ay(s/1960))-s,10<s){e.timeoutHandle=Xs(jn.bind(null,e,ot,Gt),s);break}jn(e,ot,Gt);break;case 5:jn(e,ot,Gt);break;default:throw Error(o(329))}}}return st(e,De()),e.callbackNode===i?Jf.bind(null,e):null}function $l(e,t){var i=ai;return e.current.memoizedState.isDehydrated&&(_n(e,t).flags|=256),e=Lo(e,t),e!==2&&(t=ot,ot=i,t!==null&&Hl(t)),e}function Hl(e){ot===null?ot=e:ot.push.apply(ot,e)}function uy(e){for(var t=e;;){if(t.flags&16384){var i=t.updateQueue;if(i!==null&&(i=i.stores,i!==null))for(var s=0;s<i.length;s++){var a=i[s],c=a.getSnapshot;a=a.value;try{if(!Et(c(),a))return!1}catch{return!1}}}if(i=t.child,t.subtreeFlags&16384&&i!==null)i.return=t,t=i;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function mn(e,t){for(t&=~bl,t&=~Ao,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var i=31-Tt(t),s=1<<i;e[i]=-1,t&=~s}}function ed(e){if((ce&6)!==0)throw Error(o(327));pr();var t=bi(e,0);if((t&1)===0)return st(e,De()),null;var i=Lo(e,t);if(e.tag!==0&&i===2){var s=Cs(e);s!==0&&(t=s,i=$l(e,s))}if(i===1)throw i=li,_n(e,0),mn(e,t),st(e,De()),i;if(i===6)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,jn(e,ot,Gt),st(e,De()),null}function Kl(e,t){var i=ce;ce|=1;try{return e(t)}finally{ce=i,ce===0&&(dr=De()+500,oo&&an())}}function Ln(e){pn!==null&&pn.tag===0&&(ce&6)===0&&pr();var t=ce;ce|=1;var i=wt.transition,s=me;try{if(wt.transition=null,me=1,e)return e()}finally{me=s,wt.transition=i,ce=t,(ce&6)===0&&an()}}function Gl(){dt=fr.current,Se(fr)}function _n(e,t){e.finishedWork=null,e.finishedLanes=0;var i=e.timeoutHandle;if(i!==-1&&(e.timeoutHandle=-1,Fg(i)),je!==null)for(i=je.return;i!==null;){var s=i;switch(nl(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&ro();break;case 3:ar(),Se(nt),Se(Ge),ml();break;case 5:pl(s);break;case 4:ar();break;case 13:Se(Ee);break;case 19:Se(Ee);break;case 10:al(s.type._context);break;case 22:case 23:Gl()}i=i.return}if(Ue=e,je=e=gn(e.current,null),He=dt=t,ze=0,li=null,bl=Ao=Vn=0,ot=ai=null,Rn!==null){for(t=0;t<Rn.length;t++)if(i=Rn[t],s=i.interleaved,s!==null){i.interleaved=null;var a=s.next,c=i.pending;if(c!==null){var h=c.next;c.next=a,s.next=h}i.pending=s}Rn=null}return e}function td(e,t){do{var i=je;try{if(ll(),go.current=wo,yo){for(var s=Ae.memoizedState;s!==null;){var a=s.queue;a!==null&&(a.pending=null),s=s.next}yo=!1}if(Dn=0,Be=Oe=Ae=null,ti=!1,ni=0,Fl.current=null,i===null||i.return===null){ze=1,li=t,je=null;break}e:{var c=e,h=i.return,v=i,w=t;if(t=He,v.flags|=32768,w!==null&&typeof w=="object"&&typeof w.then=="function"){var E=w,L=v,j=L.tag;if((L.mode&1)===0&&(j===0||j===11||j===15)){var D=L.alternate;D?(L.updateQueue=D.updateQueue,L.memoizedState=D.memoizedState,L.lanes=D.lanes):(L.updateQueue=null,L.memoizedState=null)}var $=Ef(h);if($!==null){$.flags&=-257,Af($,h,v,c,t),$.mode&1&&Tf(c,E,t),t=$,w=E;var K=t.updateQueue;if(K===null){var Q=new Set;Q.add(w),t.updateQueue=Q}else K.add(w);break e}else{if((t&1)===0){Tf(c,E,t),Ql();break e}w=Error(o(426))}}else if(Te&&v.mode&1){var Ve=Ef(h);if(Ve!==null){(Ve.flags&65536)===0&&(Ve.flags|=256),Af(Ve,h,v,c,t),ol(ur(w,v));break e}}c=w=ur(w,v),ze!==4&&(ze=2),ai===null?ai=[c]:ai.push(c),c=h;do{switch(c.tag){case 3:c.flags|=65536,t&=-t,c.lanes|=t;var P=Cf(c,w,t);Yc(c,P);break e;case 1:v=w;var k=c.type,T=c.stateNode;if((c.flags&128)===0&&(typeof k.getDerivedStateFromError=="function"||T!==null&&typeof T.componentDidCatch=="function"&&(dn===null||!dn.has(T)))){c.flags|=65536,t&=-t,c.lanes|=t;var O=Pf(c,v,t);Yc(c,O);break e}}c=c.return}while(c!==null)}id(i)}catch(Y){t=Y,je===i&&i!==null&&(je=i=i.return);continue}break}while(!0)}function nd(){var e=Eo.current;return Eo.current=wo,e===null?wo:e}function Ql(){(ze===0||ze===3||ze===2)&&(ze=4),Ue===null||(Vn&268435455)===0&&(Ao&268435455)===0||mn(Ue,He)}function Lo(e,t){var i=ce;ce|=2;var s=nd();(Ue!==e||He!==t)&&(Gt=null,_n(e,t));do try{cy();break}catch(a){td(e,a)}while(!0);if(ll(),ce=i,Eo.current=s,je!==null)throw Error(o(261));return Ue=null,He=0,ze}function cy(){for(;je!==null;)rd(je)}function fy(){for(;je!==null&&!Im();)rd(je)}function rd(e){var t=ld(e.alternate,e,dt);e.memoizedProps=e.pendingProps,t===null?id(e):je=t,Fl.current=null}function id(e){var t=e;do{var i=t.alternate;if(e=t.return,(t.flags&32768)===0){if(i=ry(i,t,dt),i!==null){je=i;return}}else{if(i=iy(i,t),i!==null){i.flags&=32767,je=i;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ze=6,je=null;return}}if(t=t.sibling,t!==null){je=t;return}je=t=e}while(t!==null);ze===0&&(ze=5)}function jn(e,t,i){var s=me,a=wt.transition;try{wt.transition=null,me=1,dy(e,t,i,s)}finally{wt.transition=a,me=s}return null}function dy(e,t,i,s){do pr();while(pn!==null);if((ce&6)!==0)throw Error(o(327));i=e.finishedWork;var a=e.finishedLanes;if(i===null)return null;if(e.finishedWork=null,e.finishedLanes=0,i===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var c=i.lanes|i.childLanes;if(Km(e,c),e===Ue&&(je=Ue=null,He=0),(i.subtreeFlags&2064)===0&&(i.flags&2064)===0||Ro||(Ro=!0,ad(Ii,function(){return pr(),null})),c=(i.flags&15990)!==0,(i.subtreeFlags&15990)!==0||c){c=wt.transition,wt.transition=null;var h=me;me=1;var v=ce;ce|=4,Fl.current=null,sy(e,i),Qf(i,e),Vg(Qs),Wi=!!Gs,Qs=Gs=null,e.current=i,ly(i),Om(),ce=v,me=h,wt.transition=c}else e.current=i;if(Ro&&(Ro=!1,pn=e,No=a),c=e.pendingLanes,c===0&&(dn=null),bm(i.stateNode),st(e,De()),t!==null)for(s=e.onRecoverableError,i=0;i<t.length;i++)a=t[i],s(a.value,{componentStack:a.stack,digest:a.digest});if(Mo)throw Mo=!1,e=Ul,Ul=null,e;return(No&1)!==0&&e.tag!==0&&pr(),c=e.pendingLanes,(c&1)!==0?e===Wl?ui++:(ui=0,Wl=e):ui=0,an(),null}function pr(){if(pn!==null){var e=Hu(No),t=wt.transition,i=me;try{if(wt.transition=null,me=16>e?16:e,pn===null)var s=!1;else{if(e=pn,pn=null,No=0,(ce&6)!==0)throw Error(o(331));var a=ce;for(ce|=4,H=e.current;H!==null;){var c=H,h=c.child;if((H.flags&16)!==0){var v=c.deletions;if(v!==null){for(var w=0;w<v.length;w++){var E=v[w];for(H=E;H!==null;){var L=H;switch(L.tag){case 0:case 11:case 15:si(8,L,c)}var j=L.child;if(j!==null)j.return=L,H=j;else for(;H!==null;){L=H;var D=L.sibling,$=L.return;if(Wf(L),L===E){H=null;break}if(D!==null){D.return=$,H=D;break}H=$}}}var K=c.alternate;if(K!==null){var Q=K.child;if(Q!==null){K.child=null;do{var Ve=Q.sibling;Q.sibling=null,Q=Ve}while(Q!==null)}}H=c}}if((c.subtreeFlags&2064)!==0&&h!==null)h.return=c,H=h;else e:for(;H!==null;){if(c=H,(c.flags&2048)!==0)switch(c.tag){case 0:case 11:case 15:si(9,c,c.return)}var P=c.sibling;if(P!==null){P.return=c.return,H=P;break e}H=c.return}}var k=e.current;for(H=k;H!==null;){h=H;var T=h.child;if((h.subtreeFlags&2064)!==0&&T!==null)T.return=h,H=T;else e:for(h=k;H!==null;){if(v=H,(v.flags&2048)!==0)try{switch(v.tag){case 0:case 11:case 15:To(9,v)}}catch(Y){Re(v,v.return,Y)}if(v===h){H=null;break e}var O=v.sibling;if(O!==null){O.return=v.return,H=O;break e}H=v.return}}if(ce=a,an(),Lt&&typeof Lt.onPostCommitFiberRoot=="function")try{Lt.onPostCommitFiberRoot(Oi,e)}catch{}s=!0}return s}finally{me=i,wt.transition=t}}return!1}function od(e,t,i){t=ur(i,t),t=Cf(e,t,1),e=cn(e,t,1),t=et(),e!==null&&(Lr(e,1,t),st(e,t))}function Re(e,t,i){if(e.tag===3)od(e,e,i);else for(;t!==null;){if(t.tag===3){od(t,e,i);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(dn===null||!dn.has(s))){e=ur(i,e),e=Pf(t,e,1),t=cn(t,e,1),e=et(),t!==null&&(Lr(t,1,e),st(t,e));break}}t=t.return}}function py(e,t,i){var s=e.pingCache;s!==null&&s.delete(t),t=et(),e.pingedLanes|=e.suspendedLanes&i,Ue===e&&(He&i)===i&&(ze===4||ze===3&&(He&130023424)===He&&500>De()-Bl?_n(e,0):bl|=i),st(e,t)}function sd(e,t){t===0&&((e.mode&1)===0?t=1:(t=Fi,Fi<<=1,(Fi&130023424)===0&&(Fi=4194304)));var i=et();e=$t(e,t),e!==null&&(Lr(e,t,i),st(e,i))}function hy(e){var t=e.memoizedState,i=0;t!==null&&(i=t.retryLane),sd(e,i)}function my(e,t){var i=0;switch(e.tag){case 13:var s=e.stateNode,a=e.memoizedState;a!==null&&(i=a.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(o(314))}s!==null&&s.delete(t),sd(e,i)}var ld;ld=function(e,t,i){if(e!==null)if(e.memoizedProps!==t.pendingProps||nt.current)it=!0;else{if((e.lanes&i)===0&&(t.flags&128)===0)return it=!1,ny(e,t,i);it=(e.flags&131072)!==0}else it=!1,Te&&(t.flags&1048576)!==0&&Fc(t,lo,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;Co(e,t),e=t.pendingProps;var a=tr(t,Ge.current);lr(t,i),a=vl(null,t,s,e,a,i);var c=xl();return t.flags|=1,typeof a=="object"&&a!==null&&typeof a.render=="function"&&a.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,rt(s)?(c=!0,io(t)):c=!1,t.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,fl(t),a.updater=So,t.stateNode=a,a._reactInternals=t,Tl(t,s,e,i),t=Rl(null,t,s,!0,c,i)):(t.tag=0,Te&&c&&tl(t),Je(null,t,a,i),t=t.child),t;case 16:s=t.elementType;e:{switch(Co(e,t),e=t.pendingProps,a=s._init,s=a(s._payload),t.type=s,a=t.tag=yy(s),e=Mt(s,e),a){case 0:t=Ml(null,t,s,e,i);break e;case 1:t=Lf(null,t,s,e,i);break e;case 11:t=Mf(null,t,s,e,i);break e;case 14:t=Rf(null,t,s,Mt(s.type,e),i);break e}throw Error(o(306,s,""))}return t;case 0:return s=t.type,a=t.pendingProps,a=t.elementType===s?a:Mt(s,a),Ml(e,t,s,a,i);case 1:return s=t.type,a=t.pendingProps,a=t.elementType===s?a:Mt(s,a),Lf(e,t,s,a,i);case 3:e:{if(_f(t),e===null)throw Error(o(387));s=t.pendingProps,c=t.memoizedState,a=c.element,Qc(e,t),ho(t,s,null,i);var h=t.memoizedState;if(s=h.element,c.isDehydrated)if(c={element:s,isDehydrated:!1,cache:h.cache,pendingSuspenseBoundaries:h.pendingSuspenseBoundaries,transitions:h.transitions},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){a=ur(Error(o(423)),t),t=jf(e,t,s,i,a);break e}else if(s!==a){a=ur(Error(o(424)),t),t=jf(e,t,s,i,a);break e}else for(ft=on(t.stateNode.containerInfo.firstChild),ct=t,Te=!0,At=null,i=Kc(t,null,s,i),t.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(ir(),s===a){t=Kt(e,t,i);break e}Je(e,t,s,i)}t=t.child}return t;case 5:return Zc(t),e===null&&il(t),s=t.type,a=t.pendingProps,c=e!==null?e.memoizedProps:null,h=a.children,Ys(s,a)?h=null:c!==null&&Ys(s,c)&&(t.flags|=32),Vf(e,t),Je(e,t,h,i),t.child;case 6:return e===null&&il(t),null;case 13:return If(e,t,i);case 4:return dl(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=or(t,null,s,i):Je(e,t,s,i),t.child;case 11:return s=t.type,a=t.pendingProps,a=t.elementType===s?a:Mt(s,a),Mf(e,t,s,a,i);case 7:return Je(e,t,t.pendingProps,i),t.child;case 8:return Je(e,t,t.pendingProps.children,i),t.child;case 12:return Je(e,t,t.pendingProps.children,i),t.child;case 10:e:{if(s=t.type._context,a=t.pendingProps,c=t.memoizedProps,h=a.value,xe(co,s._currentValue),s._currentValue=h,c!==null)if(Et(c.value,h)){if(c.children===a.children&&!nt.current){t=Kt(e,t,i);break e}}else for(c=t.child,c!==null&&(c.return=t);c!==null;){var v=c.dependencies;if(v!==null){h=c.child;for(var w=v.firstContext;w!==null;){if(w.context===s){if(c.tag===1){w=Ht(-1,i&-i),w.tag=2;var E=c.updateQueue;if(E!==null){E=E.shared;var L=E.pending;L===null?w.next=w:(w.next=L.next,L.next=w),E.pending=w}}c.lanes|=i,w=c.alternate,w!==null&&(w.lanes|=i),ul(c.return,i,t),v.lanes|=i;break}w=w.next}}else if(c.tag===10)h=c.type===t.type?null:c.child;else if(c.tag===18){if(h=c.return,h===null)throw Error(o(341));h.lanes|=i,v=h.alternate,v!==null&&(v.lanes|=i),ul(h,i,t),h=c.sibling}else h=c.child;if(h!==null)h.return=c;else for(h=c;h!==null;){if(h===t){h=null;break}if(c=h.sibling,c!==null){c.return=h.return,h=c;break}h=h.return}c=h}Je(e,t,a.children,i),t=t.child}return t;case 9:return a=t.type,s=t.pendingProps.children,lr(t,i),a=vt(a),s=s(a),t.flags|=1,Je(e,t,s,i),t.child;case 14:return s=t.type,a=Mt(s,t.pendingProps),a=Mt(s.type,a),Rf(e,t,s,a,i);case 15:return Nf(e,t,t.type,t.pendingProps,i);case 17:return s=t.type,a=t.pendingProps,a=t.elementType===s?a:Mt(s,a),Co(e,t),t.tag=1,rt(s)?(e=!0,io(t)):e=!1,lr(t,i),Sf(t,s,a),Tl(t,s,a,i),Rl(null,t,s,!0,e,i);case 19:return zf(e,t,i);case 22:return Df(e,t,i)}throw Error(o(156,t.tag))};function ad(e,t){return bu(e,t)}function gy(e,t,i,s){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function St(e,t,i,s){return new gy(e,t,i,s)}function Yl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function yy(e){if(typeof e=="function")return Yl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===le)return 11;if(e===tt)return 14}return 2}function gn(e,t){var i=e.alternate;return i===null?(i=St(e.tag,t,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=t,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&14680064,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,t=e.dependencies,i.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i}function _o(e,t,i,s,a,c){var h=2;if(s=e,typeof e=="function")Yl(e)&&(h=1);else if(typeof e=="string")h=5;else e:switch(e){case ne:return In(i.children,a,c,t);case X:h=8,a|=8;break;case ge:return e=St(12,i,t,a|2),e.elementType=ge,e.lanes=c,e;case be:return e=St(13,i,t,a),e.elementType=be,e.lanes=c,e;case Ie:return e=St(19,i,t,a),e.elementType=Ie,e.lanes=c,e;case ie:return jo(i,a,c,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ye:h=10;break e;case Fe:h=9;break e;case le:h=11;break e;case tt:h=14;break e;case _e:h=16,s=null;break e}throw Error(o(130,e==null?e:typeof e,""))}return t=St(h,i,t,a),t.elementType=e,t.type=s,t.lanes=c,t}function In(e,t,i,s){return e=St(7,e,s,t),e.lanes=i,e}function jo(e,t,i,s){return e=St(22,e,s,t),e.elementType=ie,e.lanes=i,e.stateNode={isHidden:!1},e}function Xl(e,t,i){return e=St(6,e,null,t),e.lanes=i,e}function Zl(e,t,i){return t=St(4,e.children!==null?e.children:[],e.key,t),t.lanes=i,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function vy(e,t,i,s,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ps(0),this.expirationTimes=Ps(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ps(0),this.identifierPrefix=s,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function ql(e,t,i,s,a,c,h,v,w){return e=new vy(e,t,i,v,w),t===1?(t=1,c===!0&&(t|=8)):t=0,c=St(3,null,null,t),e.current=c,c.stateNode=e,c.memoizedState={element:s,isDehydrated:i,cache:null,transitions:null,pendingSuspenseBoundaries:null},fl(c),e}function xy(e,t,i){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:W,key:s==null?null:""+s,children:e,containerInfo:t,implementation:i}}function ud(e){if(!e)return ln;e=e._reactInternals;e:{if(Pn(e)!==e||e.tag!==1)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(rt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(o(171))}if(e.tag===1){var i=e.type;if(rt(i))return Ic(e,i,t)}return t}function cd(e,t,i,s,a,c,h,v,w){return e=ql(i,s,!0,e,a,c,h,v,w),e.context=ud(null),i=e.current,s=et(),a=hn(i),c=Ht(s,a),c.callback=t??null,cn(i,c,a),e.current.lanes=a,Lr(e,a,s),st(e,s),e}function Io(e,t,i,s){var a=t.current,c=et(),h=hn(a);return i=ud(i),t.context===null?t.context=i:t.pendingContext=i,t=Ht(c,h),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=cn(a,t,h),e!==null&&(Dt(e,a,h,c),po(e,a,h)),h}function Oo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function fd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<t?i:t}}function Jl(e,t){fd(e,t),(e=e.alternate)&&fd(e,t)}function wy(){return null}var dd=typeof reportError=="function"?reportError:function(e){console.error(e)};function ea(e){this._internalRoot=e}zo.prototype.render=ea.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));Io(e,t,null,null)},zo.prototype.unmount=ea.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ln(function(){Io(null,e,null,null)}),t[bt]=null}};function zo(e){this._internalRoot=e}zo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qu();e={blockedOn:null,target:e,priority:t};for(var i=0;i<tn.length&&t!==0&&t<tn[i].priority;i++);tn.splice(i,0,e),i===0&&Zu(e)}};function ta(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Fo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function pd(){}function Sy(e,t,i,s,a){if(a){if(typeof s=="function"){var c=s;s=function(){var E=Oo(h);c.call(E)}}var h=cd(t,s,e,0,null,!1,!1,"",pd);return e._reactRootContainer=h,e[bt]=h.current,Gr(e.nodeType===8?e.parentNode:e),Ln(),h}for(;a=e.lastChild;)e.removeChild(a);if(typeof s=="function"){var v=s;s=function(){var E=Oo(w);v.call(E)}}var w=ql(e,0,!1,null,null,!1,!1,"",pd);return e._reactRootContainer=w,e[bt]=w.current,Gr(e.nodeType===8?e.parentNode:e),Ln(function(){Io(t,w,i,s)}),w}function bo(e,t,i,s,a){var c=i._reactRootContainer;if(c){var h=c;if(typeof a=="function"){var v=a;a=function(){var w=Oo(h);v.call(w)}}Io(t,h,e,a)}else h=Sy(i,t,e,a,s);return Oo(h)}Ku=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var i=Vr(t.pendingLanes);i!==0&&(Ts(t,i|1),st(t,De()),(ce&6)===0&&(dr=De()+500,an()))}break;case 13:Ln(function(){var s=$t(e,1);if(s!==null){var a=et();Dt(s,e,1,a)}}),Jl(e,1)}},Es=function(e){if(e.tag===13){var t=$t(e,134217728);if(t!==null){var i=et();Dt(t,e,134217728,i)}Jl(e,134217728)}},Gu=function(e){if(e.tag===13){var t=hn(e),i=$t(e,t);if(i!==null){var s=et();Dt(i,e,t,s)}Jl(e,t)}},Qu=function(){return me},Yu=function(e,t){var i=me;try{return me=e,t()}finally{me=i}},vs=function(e,t,i){switch(t){case"input":if(cs(e,i),t=i.name,i.type==="radio"&&t!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<i.length;t++){var s=i[t];if(s!==e&&s.form===e.form){var a=no(s);if(!a)throw Error(o(90));xu(s),cs(s,a)}}}break;case"textarea":Pu(e,i);break;case"select":t=i.value,t!=null&&Wn(e,!!i.multiple,t,!1)}},Lu=Kl,_u=Ln;var ky={usingClientEntryPoint:!1,Events:[Xr,Jn,no,Du,Vu,Kl]},ci={findFiberByHostInstance:Tn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Cy={bundleType:ci.bundleType,version:ci.version,rendererPackageName:ci.rendererPackageName,rendererConfig:ci.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:q.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zu(e),e===null?null:e.stateNode},findFiberByHostInstance:ci.findFiberByHostInstance||wy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Bo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Bo.isDisabled&&Bo.supportsFiber)try{Oi=Bo.inject(Cy),Lt=Bo}catch{}}return lt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ky,lt.createPortal=function(e,t){var i=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ta(t))throw Error(o(200));return xy(e,t,null,i)},lt.createRoot=function(e,t){if(!ta(e))throw Error(o(299));var i=!1,s="",a=dd;return t!=null&&(t.unstable_strictMode===!0&&(i=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(a=t.onRecoverableError)),t=ql(e,1,!1,null,null,i,!1,s,a),e[bt]=t.current,Gr(e.nodeType===8?e.parentNode:e),new ea(t)},lt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=zu(t),e=e===null?null:e.stateNode,e},lt.flushSync=function(e){return Ln(e)},lt.hydrate=function(e,t,i){if(!Fo(t))throw Error(o(200));return bo(null,e,t,!0,i)},lt.hydrateRoot=function(e,t,i){if(!ta(e))throw Error(o(405));var s=i!=null&&i.hydratedSources||null,a=!1,c="",h=dd;if(i!=null&&(i.unstable_strictMode===!0&&(a=!0),i.identifierPrefix!==void 0&&(c=i.identifierPrefix),i.onRecoverableError!==void 0&&(h=i.onRecoverableError)),t=cd(t,null,e,1,i??null,a,!1,c,h),e[bt]=t.current,Gr(e),s)for(e=0;e<s.length;e++)i=s[e],a=i._getVersion,a=a(i._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[i,a]:t.mutableSourceEagerHydrationData.push(i,a);return new zo(t)},lt.render=function(e,t,i){if(!Fo(t))throw Error(o(200));return bo(null,e,t,!1,i)},lt.unmountComponentAtNode=function(e){if(!Fo(e))throw Error(o(40));return e._reactRootContainer?(Ln(function(){bo(null,null,e,!1,function(){e._reactRootContainer=null,e[bt]=null})}),!0):!1},lt.unstable_batchedUpdates=Kl,lt.unstable_renderSubtreeIntoContainer=function(e,t,i,s){if(!Fo(i))throw Error(o(200));if(e==null||e._reactInternals===void 0)throw Error(o(38));return bo(e,t,i,!1,s)},lt.version="18.3.1-next-f1338f8080-20240426",lt}var Sd;function Ly(){if(Sd)return ia.exports;Sd=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),ia.exports=Vy(),ia.exports}var kd;function _y(){if(kd)return Uo;kd=1;var n=Ly();return Uo.createRoot=n.createRoot,Uo.hydrateRoot=n.hydrateRoot,Uo}var jy=_y();function Wp(n){var r,o,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(r=0;r<u;r++)n[r]&&(o=Wp(n[r]))&&(l&&(l+=" "),l+=o)}else for(o in n)n[o]&&(l&&(l+=" "),l+=o);return l}function $p(){for(var n,r,o=0,l="",u=arguments.length;o<u;o++)(n=arguments[o])&&(r=Wp(n))&&(l&&(l+=" "),l+=r);return l}const ba="-",Iy=n=>{const r=zy(n),{conflictingClassGroups:o,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:f=>{const p=f.split(ba);return p[0]===""&&p.length!==1&&p.shift(),Hp(p,r)||Oy(f)},getConflictingClassGroupIds:(f,p)=>{const m=o[f]||[];return p&&l[f]?[...m,...l[f]]:m}}},Hp=(n,r)=>{var f;if(n.length===0)return r.classGroupId;const o=n[0],l=r.nextPart.get(o),u=l?Hp(n.slice(1),l):void 0;if(u)return u;if(r.validators.length===0)return;const d=n.join(ba);return(f=r.validators.find(({validator:p})=>p(d)))==null?void 0:f.classGroupId},Cd=/^\[(.+)\]$/,Oy=n=>{if(Cd.test(n)){const r=Cd.exec(n)[1],o=r==null?void 0:r.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},zy=n=>{const{theme:r,prefix:o}=n,l={nextPart:new Map,validators:[]};return by(Object.entries(n.classGroups),o).forEach(([d,f])=>{Sa(f,l,d,r)}),l},Sa=(n,r,o,l)=>{n.forEach(u=>{if(typeof u=="string"){const d=u===""?r:Pd(r,u);d.classGroupId=o;return}if(typeof u=="function"){if(Fy(u)){Sa(u(l),r,o,l);return}r.validators.push({validator:u,classGroupId:o});return}Object.entries(u).forEach(([d,f])=>{Sa(f,Pd(r,d),o,l)})})},Pd=(n,r)=>{let o=n;return r.split(ba).forEach(l=>{o.nextPart.has(l)||o.nextPart.set(l,{nextPart:new Map,validators:[]}),o=o.nextPart.get(l)}),o},Fy=n=>n.isThemeGetter,by=(n,r)=>r?n.map(([o,l])=>{const u=l.map(d=>typeof d=="string"?r+d:typeof d=="object"?Object.fromEntries(Object.entries(d).map(([f,p])=>[r+f,p])):d);return[o,u]}):n,By=n=>{if(n<1)return{get:()=>{},set:()=>{}};let r=0,o=new Map,l=new Map;const u=(d,f)=>{o.set(d,f),r++,r>n&&(r=0,l=o,o=new Map)};return{get(d){let f=o.get(d);if(f!==void 0)return f;if((f=l.get(d))!==void 0)return u(d,f),f},set(d,f){o.has(d)?o.set(d,f):u(d,f)}}},Kp="!",Uy=n=>{const{separator:r,experimentalParseClassName:o}=n,l=r.length===1,u=r[0],d=r.length,f=p=>{const m=[];let g=0,y=0,x;for(let R=0;R<p.length;R++){let _=p[R];if(g===0){if(_===u&&(l||p.slice(R,R+d)===r)){m.push(p.slice(y,R)),y=R+d;continue}if(_==="/"){x=R;continue}}_==="["?g++:_==="]"&&g--}const S=m.length===0?p:p.substring(y),A=S.startsWith(Kp),N=A?S.substring(1):S,M=x&&x>y?x-y:void 0;return{modifiers:m,hasImportantModifier:A,baseClassName:N,maybePostfixModifierPosition:M}};return o?p=>o({className:p,parseClassName:f}):f},Wy=n=>{if(n.length<=1)return n;const r=[];let o=[];return n.forEach(l=>{l[0]==="["?(r.push(...o.sort(),l),o=[]):o.push(l)}),r.push(...o.sort()),r},$y=n=>({cache:By(n.cacheSize),parseClassName:Uy(n),...Iy(n)}),Hy=/\s+/,Ky=(n,r)=>{const{parseClassName:o,getClassGroupId:l,getConflictingClassGroupIds:u}=r,d=[],f=n.trim().split(Hy);let p="";for(let m=f.length-1;m>=0;m-=1){const g=f[m],{modifiers:y,hasImportantModifier:x,baseClassName:S,maybePostfixModifierPosition:A}=o(g);let N=!!A,M=l(N?S.substring(0,A):S);if(!M){if(!N){p=g+(p.length>0?" "+p:p);continue}if(M=l(S),!M){p=g+(p.length>0?" "+p:p);continue}N=!1}const R=Wy(y).join(":"),_=x?R+Kp:R,F=_+M;if(d.includes(F))continue;d.push(F);const U=u(M,N);for(let q=0;q<U.length;++q){const G=U[q];d.push(_+G)}p=g+(p.length>0?" "+p:p)}return p};function Gy(){let n=0,r,o,l="";for(;n<arguments.length;)(r=arguments[n++])&&(o=Gp(r))&&(l&&(l+=" "),l+=o);return l}const Gp=n=>{if(typeof n=="string")return n;let r,o="";for(let l=0;l<n.length;l++)n[l]&&(r=Gp(n[l]))&&(o&&(o+=" "),o+=r);return o};function Qy(n,...r){let o,l,u,d=f;function f(m){const g=r.reduce((y,x)=>x(y),n());return o=$y(g),l=o.cache.get,u=o.cache.set,d=p,p(m)}function p(m){const g=l(m);if(g)return g;const y=Ky(m,o);return u(m,y),y}return function(){return d(Gy.apply(null,arguments))}}const ke=n=>{const r=o=>o[n]||[];return r.isThemeGetter=!0,r},Qp=/^\[(?:([a-z-]+):)?(.+)\]$/i,Yy=/^\d+\/\d+$/,Xy=new Set(["px","full","screen"]),Zy=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,qy=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Jy=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ev=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,tv=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Qt=n=>xr(n)||Xy.has(n)||Yy.test(n),vn=n=>Cr(n,"length",uv),xr=n=>!!n&&!Number.isNaN(Number(n)),la=n=>Cr(n,"number",xr),di=n=>!!n&&Number.isInteger(Number(n)),nv=n=>n.endsWith("%")&&xr(n.slice(0,-1)),oe=n=>Qp.test(n),xn=n=>Zy.test(n),rv=new Set(["length","size","percentage"]),iv=n=>Cr(n,rv,Yp),ov=n=>Cr(n,"position",Yp),sv=new Set(["image","url"]),lv=n=>Cr(n,sv,fv),av=n=>Cr(n,"",cv),pi=()=>!0,Cr=(n,r,o)=>{const l=Qp.exec(n);return l?l[1]?typeof r=="string"?l[1]===r:r.has(l[1]):o(l[2]):!1},uv=n=>qy.test(n)&&!Jy.test(n),Yp=()=>!1,cv=n=>ev.test(n),fv=n=>tv.test(n),dv=()=>{const n=ke("colors"),r=ke("spacing"),o=ke("blur"),l=ke("brightness"),u=ke("borderColor"),d=ke("borderRadius"),f=ke("borderSpacing"),p=ke("borderWidth"),m=ke("contrast"),g=ke("grayscale"),y=ke("hueRotate"),x=ke("invert"),S=ke("gap"),A=ke("gradientColorStops"),N=ke("gradientColorStopPositions"),M=ke("inset"),R=ke("margin"),_=ke("opacity"),F=ke("padding"),U=ke("saturate"),q=ke("scale"),G=ke("sepia"),W=ke("skew"),ne=ke("space"),X=ke("translate"),ge=()=>["auto","contain","none"],ye=()=>["auto","hidden","clip","visible","scroll"],Fe=()=>["auto",oe,r],le=()=>[oe,r],be=()=>["",Qt,vn],Ie=()=>["auto",xr,oe],tt=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],_e=()=>["solid","dashed","dotted","double","none"],ie=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],z=()=>["start","end","center","between","around","evenly","stretch"],Z=()=>["","0",oe],B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>[xr,oe];return{cacheSize:500,separator:":",theme:{colors:[pi],spacing:[Qt,vn],blur:["none","",xn,oe],brightness:C(),borderColor:[n],borderRadius:["none","","full",xn,oe],borderSpacing:le(),borderWidth:be(),contrast:C(),grayscale:Z(),hueRotate:C(),invert:Z(),gap:le(),gradientColorStops:[n],gradientColorStopPositions:[nv,vn],inset:Fe(),margin:Fe(),opacity:C(),padding:le(),saturate:C(),scale:C(),sepia:Z(),skew:C(),space:le(),translate:le()},classGroups:{aspect:[{aspect:["auto","square","video",oe]}],container:["container"],columns:[{columns:[xn]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...tt(),oe]}],overflow:[{overflow:ye()}],"overflow-x":[{"overflow-x":ye()}],"overflow-y":[{"overflow-y":ye()}],overscroll:[{overscroll:ge()}],"overscroll-x":[{"overscroll-x":ge()}],"overscroll-y":[{"overscroll-y":ge()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[M]}],"inset-x":[{"inset-x":[M]}],"inset-y":[{"inset-y":[M]}],start:[{start:[M]}],end:[{end:[M]}],top:[{top:[M]}],right:[{right:[M]}],bottom:[{bottom:[M]}],left:[{left:[M]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",di,oe]}],basis:[{basis:Fe()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",oe]}],grow:[{grow:Z()}],shrink:[{shrink:Z()}],order:[{order:["first","last","none",di,oe]}],"grid-cols":[{"grid-cols":[pi]}],"col-start-end":[{col:["auto",{span:["full",di,oe]},oe]}],"col-start":[{"col-start":Ie()}],"col-end":[{"col-end":Ie()}],"grid-rows":[{"grid-rows":[pi]}],"row-start-end":[{row:["auto",{span:[di,oe]},oe]}],"row-start":[{"row-start":Ie()}],"row-end":[{"row-end":Ie()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",oe]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",oe]}],gap:[{gap:[S]}],"gap-x":[{"gap-x":[S]}],"gap-y":[{"gap-y":[S]}],"justify-content":[{justify:["normal",...z()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...z(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...z(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[F]}],px:[{px:[F]}],py:[{py:[F]}],ps:[{ps:[F]}],pe:[{pe:[F]}],pt:[{pt:[F]}],pr:[{pr:[F]}],pb:[{pb:[F]}],pl:[{pl:[F]}],m:[{m:[R]}],mx:[{mx:[R]}],my:[{my:[R]}],ms:[{ms:[R]}],me:[{me:[R]}],mt:[{mt:[R]}],mr:[{mr:[R]}],mb:[{mb:[R]}],ml:[{ml:[R]}],"space-x":[{"space-x":[ne]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[ne]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",oe,r]}],"min-w":[{"min-w":[oe,r,"min","max","fit"]}],"max-w":[{"max-w":[oe,r,"none","full","min","max","fit","prose",{screen:[xn]},xn]}],h:[{h:[oe,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[oe,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[oe,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[oe,r,"auto","min","max","fit"]}],"font-size":[{text:["base",xn,vn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",la]}],"font-family":[{font:[pi]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",oe]}],"line-clamp":[{"line-clamp":["none",xr,la]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Qt,oe]}],"list-image":[{"list-image":["none",oe]}],"list-style-type":[{list:["none","disc","decimal",oe]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[n]}],"placeholder-opacity":[{"placeholder-opacity":[_]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[n]}],"text-opacity":[{"text-opacity":[_]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[..._e(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Qt,vn]}],"underline-offset":[{"underline-offset":["auto",Qt,oe]}],"text-decoration-color":[{decoration:[n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:le()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",oe]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",oe]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[_]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...tt(),ov]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",iv]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},lv]}],"bg-color":[{bg:[n]}],"gradient-from-pos":[{from:[N]}],"gradient-via-pos":[{via:[N]}],"gradient-to-pos":[{to:[N]}],"gradient-from":[{from:[A]}],"gradient-via":[{via:[A]}],"gradient-to":[{to:[A]}],rounded:[{rounded:[d]}],"rounded-s":[{"rounded-s":[d]}],"rounded-e":[{"rounded-e":[d]}],"rounded-t":[{"rounded-t":[d]}],"rounded-r":[{"rounded-r":[d]}],"rounded-b":[{"rounded-b":[d]}],"rounded-l":[{"rounded-l":[d]}],"rounded-ss":[{"rounded-ss":[d]}],"rounded-se":[{"rounded-se":[d]}],"rounded-ee":[{"rounded-ee":[d]}],"rounded-es":[{"rounded-es":[d]}],"rounded-tl":[{"rounded-tl":[d]}],"rounded-tr":[{"rounded-tr":[d]}],"rounded-br":[{"rounded-br":[d]}],"rounded-bl":[{"rounded-bl":[d]}],"border-w":[{border:[p]}],"border-w-x":[{"border-x":[p]}],"border-w-y":[{"border-y":[p]}],"border-w-s":[{"border-s":[p]}],"border-w-e":[{"border-e":[p]}],"border-w-t":[{"border-t":[p]}],"border-w-r":[{"border-r":[p]}],"border-w-b":[{"border-b":[p]}],"border-w-l":[{"border-l":[p]}],"border-opacity":[{"border-opacity":[_]}],"border-style":[{border:[..._e(),"hidden"]}],"divide-x":[{"divide-x":[p]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[p]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[_]}],"divide-style":[{divide:_e()}],"border-color":[{border:[u]}],"border-color-x":[{"border-x":[u]}],"border-color-y":[{"border-y":[u]}],"border-color-s":[{"border-s":[u]}],"border-color-e":[{"border-e":[u]}],"border-color-t":[{"border-t":[u]}],"border-color-r":[{"border-r":[u]}],"border-color-b":[{"border-b":[u]}],"border-color-l":[{"border-l":[u]}],"divide-color":[{divide:[u]}],"outline-style":[{outline:["",..._e()]}],"outline-offset":[{"outline-offset":[Qt,oe]}],"outline-w":[{outline:[Qt,vn]}],"outline-color":[{outline:[n]}],"ring-w":[{ring:be()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[n]}],"ring-opacity":[{"ring-opacity":[_]}],"ring-offset-w":[{"ring-offset":[Qt,vn]}],"ring-offset-color":[{"ring-offset":[n]}],shadow:[{shadow:["","inner","none",xn,av]}],"shadow-color":[{shadow:[pi]}],opacity:[{opacity:[_]}],"mix-blend":[{"mix-blend":[...ie(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ie()}],filter:[{filter:["","none"]}],blur:[{blur:[o]}],brightness:[{brightness:[l]}],contrast:[{contrast:[m]}],"drop-shadow":[{"drop-shadow":["","none",xn,oe]}],grayscale:[{grayscale:[g]}],"hue-rotate":[{"hue-rotate":[y]}],invert:[{invert:[x]}],saturate:[{saturate:[U]}],sepia:[{sepia:[G]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[o]}],"backdrop-brightness":[{"backdrop-brightness":[l]}],"backdrop-contrast":[{"backdrop-contrast":[m]}],"backdrop-grayscale":[{"backdrop-grayscale":[g]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[y]}],"backdrop-invert":[{"backdrop-invert":[x]}],"backdrop-opacity":[{"backdrop-opacity":[_]}],"backdrop-saturate":[{"backdrop-saturate":[U]}],"backdrop-sepia":[{"backdrop-sepia":[G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[f]}],"border-spacing-x":[{"border-spacing-x":[f]}],"border-spacing-y":[{"border-spacing-y":[f]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",oe]}],duration:[{duration:C()}],ease:[{ease:["linear","in","out","in-out",oe]}],delay:[{delay:C()}],animate:[{animate:["none","spin","ping","pulse","bounce",oe]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[q]}],"scale-x":[{"scale-x":[q]}],"scale-y":[{"scale-y":[q]}],rotate:[{rotate:[di,oe]}],"translate-x":[{"translate-x":[X]}],"translate-y":[{"translate-y":[X]}],"skew-x":[{"skew-x":[W]}],"skew-y":[{"skew-y":[W]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",oe]}],accent:[{accent:["auto",n]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",oe]}],"caret-color":[{caret:[n]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":le()}],"scroll-mx":[{"scroll-mx":le()}],"scroll-my":[{"scroll-my":le()}],"scroll-ms":[{"scroll-ms":le()}],"scroll-me":[{"scroll-me":le()}],"scroll-mt":[{"scroll-mt":le()}],"scroll-mr":[{"scroll-mr":le()}],"scroll-mb":[{"scroll-mb":le()}],"scroll-ml":[{"scroll-ml":le()}],"scroll-p":[{"scroll-p":le()}],"scroll-px":[{"scroll-px":le()}],"scroll-py":[{"scroll-py":le()}],"scroll-ps":[{"scroll-ps":le()}],"scroll-pe":[{"scroll-pe":le()}],"scroll-pt":[{"scroll-pt":le()}],"scroll-pr":[{"scroll-pr":le()}],"scroll-pb":[{"scroll-pb":le()}],"scroll-pl":[{"scroll-pl":le()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",oe]}],fill:[{fill:[n,"none"]}],"stroke-w":[{stroke:[Qt,vn,la]}],stroke:[{stroke:[n,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},pv=Qy(dv);function rs(...n){return pv($p(n))}function ka({className:n,...r}){return I.jsx("div",{"data-slot":"card",className:rs("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border",n),...r})}function Xp({className:n,type:r,...o}){return I.jsx("input",{type:r,"data-slot":"input",className:rs("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border px-3 py-1 text-base bg-input-background transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n),...o})}function Td(n,r){if(typeof n=="function")return n(r);n!=null&&(n.current=r)}function hv(...n){return r=>{let o=!1;const l=n.map(u=>{const d=Td(u,r);return!o&&typeof d=="function"&&(o=!0),d});if(o)return()=>{for(let u=0;u<l.length;u++){const d=l[u];typeof d=="function"?d():Td(n[u],null)}}}}function mv(n){const r=gv(n),o=b.forwardRef((l,u)=>{const{children:d,...f}=l,p=b.Children.toArray(d),m=p.find(vv);if(m){const g=m.props.children,y=p.map(x=>x===m?b.Children.count(g)>1?b.Children.only(null):b.isValidElement(g)?g.props.children:null:x);return I.jsx(r,{...f,ref:u,children:b.isValidElement(g)?b.cloneElement(g,void 0,y):null})}return I.jsx(r,{...f,ref:u,children:d})});return o.displayName=`${n}.Slot`,o}var Zp=mv("Slot");function gv(n){const r=b.forwardRef((o,l)=>{const{children:u,...d}=o;if(b.isValidElement(u)){const f=wv(u),p=xv(d,u.props);return u.type!==b.Fragment&&(p.ref=l?hv(l,f):f),b.cloneElement(u,p)}return b.Children.count(u)>1?b.Children.only(null):null});return r.displayName=`${n}.SlotClone`,r}var yv=Symbol("radix.slottable");function vv(n){return b.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===yv}function xv(n,r){const o={...r};for(const l in r){const u=n[l],d=r[l];/^on[A-Z]/.test(l)?u&&d?o[l]=(...p)=>{const m=d(...p);return u(...p),m}:u&&(o[l]=u):l==="style"?o[l]={...u,...d}:l==="className"&&(o[l]=[u,d].filter(Boolean).join(" "))}return{...n,...o}}function wv(n){var l,u;let r=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?n.ref:(r=(u=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:u.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o?n.props.ref:n.props.ref||n.ref)}const Ed=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,Ad=$p,qp=(n,r)=>o=>{var l;if((r==null?void 0:r.variants)==null)return Ad(n,o==null?void 0:o.class,o==null?void 0:o.className);const{variants:u,defaultVariants:d}=r,f=Object.keys(u).map(g=>{const y=o==null?void 0:o[g],x=d==null?void 0:d[g];if(y===null)return null;const S=Ed(y)||Ed(x);return u[g][S]}),p=o&&Object.entries(o).reduce((g,y)=>{let[x,S]=y;return S===void 0||(g[x]=S),g},{}),m=r==null||(l=r.compoundVariants)===null||l===void 0?void 0:l.reduce((g,y)=>{let{class:x,className:S,...A}=y;return Object.entries(A).every(N=>{let[M,R]=N;return Array.isArray(R)?R.includes({...d,...p}[M]):{...d,...p}[M]===R})?[...g,x,S]:g},[]);return Ad(n,f,m,o==null?void 0:o.class,o==null?void 0:o.className)},Sv=qp("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background text-foreground hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9 rounded-md"}},defaultVariants:{variant:"default",size:"default"}});function Yo({className:n,variant:r,size:o,asChild:l=!1,...u}){const d=l?Zp:"button";return I.jsx(d,{"data-slot":"button",className:rs(Sv({variant:r,size:o,className:n})),...u})}const Jp=b.createContext({});function kv(n){const r=b.useRef(null);return r.current===null&&(r.current=n()),r.current}const Ba=b.createContext(null),eh=b.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});function Cv(n=!0){const r=b.useContext(Ba);if(r===null)return[!0,null];const{isPresent:o,onExitComplete:l,register:u}=r,d=b.useId();b.useEffect(()=>{n&&u(d)},[n]);const f=b.useCallback(()=>n&&l&&l(d),[d,l,n]);return!o&&l?[!1,f]:[!0]}const Ua=typeof window<"u",Pv=Ua?b.useLayoutEffect:b.useEffect,pt=n=>n;let th=pt;function Wa(n){let r;return()=>(r===void 0&&(r=n()),r)}const wr=(n,r,o)=>{const l=r-n;return l===0?1:(o-n)/l},Yt=n=>n*1e3,Xt=n=>n/1e3,Tv={useManualTiming:!1};function Ev(n){let r=new Set,o=new Set,l=!1,u=!1;const d=new WeakSet;let f={delta:0,timestamp:0,isProcessing:!1};function p(g){d.has(g)&&(m.schedule(g),n()),g(f)}const m={schedule:(g,y=!1,x=!1)=>{const A=x&&l?r:o;return y&&d.add(g),A.has(g)||A.add(g),g},cancel:g=>{o.delete(g),d.delete(g)},process:g=>{if(f=g,l){u=!0;return}l=!0,[r,o]=[o,r],r.forEach(p),r.clear(),l=!1,u&&(u=!1,m.process(g))}};return m}const Wo=["read","resolveKeyframes","update","preRender","render","postRender"],Av=40;function nh(n,r){let o=!1,l=!0;const u={delta:0,timestamp:0,isProcessing:!1},d=()=>o=!0,f=Wo.reduce((_,F)=>(_[F]=Ev(d),_),{}),{read:p,resolveKeyframes:m,update:g,preRender:y,render:x,postRender:S}=f,A=()=>{const _=performance.now();o=!1,u.delta=l?1e3/60:Math.max(Math.min(_-u.timestamp,Av),1),u.timestamp=_,u.isProcessing=!0,p.process(u),m.process(u),g.process(u),y.process(u),x.process(u),S.process(u),u.isProcessing=!1,o&&r&&(l=!1,n(A))},N=()=>{o=!0,l=!0,u.isProcessing||n(A)};return{schedule:Wo.reduce((_,F)=>{const U=f[F];return _[F]=(q,G=!1,W=!1)=>(o||N(),U.schedule(q,G,W)),_},{}),cancel:_=>{for(let F=0;F<Wo.length;F++)f[Wo[F]].cancel(_)},state:u,steps:f}}const{schedule:Ce,cancel:Sn,state:Ke,steps:aa}=nh(typeof requestAnimationFrame<"u"?requestAnimationFrame:pt,!0),rh=b.createContext({strict:!1}),Md={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Sr={};for(const n in Md)Sr[n]={isEnabled:r=>Md[n].some(o=>!!r[o])};function Mv(n){for(const r in n)Sr[r]={...Sr[r],...n[r]}}const Rv=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Xo(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||Rv.has(n)}let ih=n=>!Xo(n);function Nv(n){n&&(ih=r=>r.startsWith("on")?!Xo(r):n(r))}try{Nv(require("@emotion/is-prop-valid").default)}catch{}function Dv(n,r,o){const l={};for(const u in n)u==="values"&&typeof n.values=="object"||(ih(u)||o===!0&&Xo(u)||!r&&!Xo(u)||n.draggable&&u.startsWith("onDrag"))&&(l[u]=n[u]);return l}function Vv(n){if(typeof Proxy>"u")return n;const r=new Map,o=(...l)=>n(...l);return new Proxy(o,{get:(l,u)=>u==="create"?n:(r.has(u)||r.set(u,n(u)),r.get(u))})}const is=b.createContext({});function ki(n){return typeof n=="string"||Array.isArray(n)}function os(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}const $a=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ha=["initial",...$a];function ss(n){return os(n.animate)||Ha.some(r=>ki(n[r]))}function oh(n){return!!(ss(n)||n.variants)}function Lv(n,r){if(ss(n)){const{initial:o,animate:l}=n;return{initial:o===!1||ki(o)?o:void 0,animate:ki(l)?l:void 0}}return n.inherit!==!1?r:{}}function _v(n){const{initial:r,animate:o}=Lv(n,b.useContext(is));return b.useMemo(()=>({initial:r,animate:o}),[Rd(r),Rd(o)])}function Rd(n){return Array.isArray(n)?n.join(" "):n}const jv=Symbol.for("motionComponentSymbol");function hr(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function Iv(n,r,o){return b.useCallback(l=>{l&&n.onMount&&n.onMount(l),r&&(l?r.mount(l):r.unmount()),o&&(typeof o=="function"?o(l):hr(o)&&(o.current=l))},[r])}const Ka=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Ov="framerAppearId",sh="data-"+Ka(Ov),{schedule:Ga}=nh(queueMicrotask,!1),lh=b.createContext({});function zv(n,r,o,l,u){var d,f;const{visualElement:p}=b.useContext(is),m=b.useContext(rh),g=b.useContext(Ba),y=b.useContext(eh).reducedMotion,x=b.useRef(null);l=l||m.renderer,!x.current&&l&&(x.current=l(n,{visualState:r,parent:p,props:o,presenceContext:g,blockInitialAnimation:g?g.initial===!1:!1,reducedMotionConfig:y}));const S=x.current,A=b.useContext(lh);S&&!S.projection&&u&&(S.type==="html"||S.type==="svg")&&Fv(x.current,o,u,A);const N=b.useRef(!1);b.useInsertionEffect(()=>{S&&N.current&&S.update(o,g)});const M=o[sh],R=b.useRef(!!M&&!(!((d=window.MotionHandoffIsComplete)===null||d===void 0)&&d.call(window,M))&&((f=window.MotionHasOptimisedAnimation)===null||f===void 0?void 0:f.call(window,M)));return Pv(()=>{S&&(N.current=!0,window.MotionIsMounted=!0,S.updateFeatures(),Ga.render(S.render),R.current&&S.animationState&&S.animationState.animateChanges())}),b.useEffect(()=>{S&&(!R.current&&S.animationState&&S.animationState.animateChanges(),R.current&&(queueMicrotask(()=>{var _;(_=window.MotionHandoffMarkAsComplete)===null||_===void 0||_.call(window,M)}),R.current=!1))}),S}function Fv(n,r,o,l){const{layoutId:u,layout:d,drag:f,dragConstraints:p,layoutScroll:m,layoutRoot:g}=r;n.projection=new o(n.latestValues,r["data-framer-portal-id"]?void 0:ah(n.parent)),n.projection.setOptions({layoutId:u,layout:d,alwaysMeasureLayout:!!f||p&&hr(p),visualElement:n,animationType:typeof d=="string"?d:"both",initialPromotionConfig:l,layoutScroll:m,layoutRoot:g})}function ah(n){if(n)return n.options.allowProjection!==!1?n.projection:ah(n.parent)}function bv({preloadedFeatures:n,createVisualElement:r,useRender:o,useVisualState:l,Component:u}){var d,f;n&&Mv(n);function p(g,y){let x;const S={...b.useContext(eh),...g,layoutId:Bv(g)},{isStatic:A}=S,N=_v(g),M=l(g,A);if(!A&&Ua){Uv();const R=Wv(S);x=R.MeasureLayout,N.visualElement=zv(u,M,S,r,R.ProjectionNode)}return I.jsxs(is.Provider,{value:N,children:[x&&N.visualElement?I.jsx(x,{visualElement:N.visualElement,...S}):null,o(u,g,Iv(M,N.visualElement,y),M,A,N.visualElement)]})}p.displayName=`motion.${typeof u=="string"?u:`create(${(f=(d=u.displayName)!==null&&d!==void 0?d:u.name)!==null&&f!==void 0?f:""})`}`;const m=b.forwardRef(p);return m[jv]=u,m}function Bv({layoutId:n}){const r=b.useContext(Jp).id;return r&&n!==void 0?r+"-"+n:n}function Uv(n,r){b.useContext(rh).strict}function Wv(n){const{drag:r,layout:o}=Sr;if(!r&&!o)return{};const l={...r,...o};return{MeasureLayout:r!=null&&r.isEnabled(n)||o!=null&&o.isEnabled(n)?l.MeasureLayout:void 0,ProjectionNode:l.ProjectionNode}}const $v=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Qa(n){return typeof n!="string"||n.includes("-")?!1:!!($v.indexOf(n)>-1||/[A-Z]/u.test(n))}function Nd(n){const r=[{},{}];return n==null||n.values.forEach((o,l)=>{r[0][l]=o.get(),r[1][l]=o.getVelocity()}),r}function Ya(n,r,o,l){if(typeof r=="function"){const[u,d]=Nd(l);r=r(o!==void 0?o:n.custom,u,d)}if(typeof r=="string"&&(r=n.variants&&n.variants[r]),typeof r=="function"){const[u,d]=Nd(l);r=r(o!==void 0?o:n.custom,u,d)}return r}const Ca=n=>Array.isArray(n),Hv=n=>!!(n&&typeof n=="object"&&n.mix&&n.toValue),Kv=n=>Ca(n)?n[n.length-1]||0:n,qe=n=>!!(n&&n.getVelocity);function Ko(n){const r=qe(n)?n.get():n;return Hv(r)?r.toValue():r}function Gv({scrapeMotionValuesFromProps:n,createRenderState:r,onUpdate:o},l,u,d){const f={latestValues:Qv(l,u,d,n),renderState:r()};return o&&(f.onMount=p=>o({props:l,current:p,...f}),f.onUpdate=p=>o(p)),f}const uh=n=>(r,o)=>{const l=b.useContext(is),u=b.useContext(Ba),d=()=>Gv(n,r,l,u);return o?d():kv(d)};function Qv(n,r,o,l){const u={},d=l(n,{});for(const S in d)u[S]=Ko(d[S]);let{initial:f,animate:p}=n;const m=ss(n),g=oh(n);r&&g&&!m&&n.inherit!==!1&&(f===void 0&&(f=r.initial),p===void 0&&(p=r.animate));let y=o?o.initial===!1:!1;y=y||f===!1;const x=y?p:f;if(x&&typeof x!="boolean"&&!os(x)){const S=Array.isArray(x)?x:[x];for(let A=0;A<S.length;A++){const N=Ya(n,S[A]);if(N){const{transitionEnd:M,transition:R,..._}=N;for(const F in _){let U=_[F];if(Array.isArray(U)){const q=y?U.length-1:0;U=U[q]}U!==null&&(u[F]=U)}for(const F in M)u[F]=M[F]}}}return u}const Pr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Un=new Set(Pr),ch=n=>r=>typeof r=="string"&&r.startsWith(n),fh=ch("--"),Yv=ch("var(--"),Xa=n=>Yv(n)?Xv.test(n.split("/*")[0].trim()):!1,Xv=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,dh=(n,r)=>r&&typeof n=="number"?r.transform(n):n,Zt=(n,r,o)=>o>r?r:o<n?n:o,Tr={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Ci={...Tr,transform:n=>Zt(0,1,n)},$o={...Tr,default:1},Ai=n=>({test:r=>typeof r=="string"&&r.endsWith(n)&&r.split(" ").length===1,parse:parseFloat,transform:r=>`${r}${n}`}),wn=Ai("deg"),zt=Ai("%"),te=Ai("px"),Zv=Ai("vh"),qv=Ai("vw"),Dd={...zt,parse:n=>zt.parse(n)/100,transform:n=>zt.transform(n*100)},Jv={borderWidth:te,borderTopWidth:te,borderRightWidth:te,borderBottomWidth:te,borderLeftWidth:te,borderRadius:te,radius:te,borderTopLeftRadius:te,borderTopRightRadius:te,borderBottomRightRadius:te,borderBottomLeftRadius:te,width:te,maxWidth:te,height:te,maxHeight:te,top:te,right:te,bottom:te,left:te,padding:te,paddingTop:te,paddingRight:te,paddingBottom:te,paddingLeft:te,margin:te,marginTop:te,marginRight:te,marginBottom:te,marginLeft:te,backgroundPositionX:te,backgroundPositionY:te},e0={rotate:wn,rotateX:wn,rotateY:wn,rotateZ:wn,scale:$o,scaleX:$o,scaleY:$o,scaleZ:$o,skew:wn,skewX:wn,skewY:wn,distance:te,translateX:te,translateY:te,translateZ:te,x:te,y:te,z:te,perspective:te,transformPerspective:te,opacity:Ci,originX:Dd,originY:Dd,originZ:te},Vd={...Tr,transform:Math.round},Za={...Jv,...e0,zIndex:Vd,size:te,fillOpacity:Ci,strokeOpacity:Ci,numOctaves:Vd},t0={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},n0=Pr.length;function r0(n,r,o){let l="",u=!0;for(let d=0;d<n0;d++){const f=Pr[d],p=n[f];if(p===void 0)continue;let m=!0;if(typeof p=="number"?m=p===(f.startsWith("scale")?1:0):m=parseFloat(p)===0,!m||o){const g=dh(p,Za[f]);if(!m){u=!1;const y=t0[f]||f;l+=`${y}(${g}) `}o&&(r[f]=g)}}return l=l.trim(),o?l=o(r,u?"":l):u&&(l="none"),l}function qa(n,r,o){const{style:l,vars:u,transformOrigin:d}=n;let f=!1,p=!1;for(const m in r){const g=r[m];if(Un.has(m)){f=!0;continue}else if(fh(m)){u[m]=g;continue}else{const y=dh(g,Za[m]);m.startsWith("origin")?(p=!0,d[m]=y):l[m]=y}}if(r.transform||(f||o?l.transform=r0(r,n.transform,o):l.transform&&(l.transform="none")),p){const{originX:m="50%",originY:g="50%",originZ:y=0}=d;l.transformOrigin=`${m} ${g} ${y}`}}const i0={offset:"stroke-dashoffset",array:"stroke-dasharray"},o0={offset:"strokeDashoffset",array:"strokeDasharray"};function s0(n,r,o=1,l=0,u=!0){n.pathLength=1;const d=u?i0:o0;n[d.offset]=te.transform(-l);const f=te.transform(r),p=te.transform(o);n[d.array]=`${f} ${p}`}function Ld(n,r,o){return typeof n=="string"?n:te.transform(r+o*n)}function l0(n,r,o){const l=Ld(r,n.x,n.width),u=Ld(o,n.y,n.height);return`${l} ${u}`}function Ja(n,{attrX:r,attrY:o,attrScale:l,originX:u,originY:d,pathLength:f,pathSpacing:p=1,pathOffset:m=0,...g},y,x){if(qa(n,g,x),y){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:S,style:A,dimensions:N}=n;S.transform&&(N&&(A.transform=S.transform),delete S.transform),N&&(u!==void 0||d!==void 0||A.transform)&&(A.transformOrigin=l0(N,u!==void 0?u:.5,d!==void 0?d:.5)),r!==void 0&&(S.x=r),o!==void 0&&(S.y=o),l!==void 0&&(S.scale=l),f!==void 0&&s0(S,f,p,m,!1)}const eu=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),ph=()=>({...eu(),attrs:{}}),tu=n=>typeof n=="string"&&n.toLowerCase()==="svg";function hh(n,{style:r,vars:o},l,u){Object.assign(n.style,r,u&&u.getProjectionStyles(l));for(const d in o)n.style.setProperty(d,o[d])}const mh=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function gh(n,r,o,l){hh(n,r,void 0,l);for(const u in r.attrs)n.setAttribute(mh.has(u)?u:Ka(u),r.attrs[u])}const Zo={};function a0(n){Object.assign(Zo,n)}function yh(n,{layout:r,layoutId:o}){return Un.has(n)||n.startsWith("origin")||(r||o!==void 0)&&(!!Zo[n]||n==="opacity")}function nu(n,r,o){var l;const{style:u}=n,d={};for(const f in u)(qe(u[f])||r.style&&qe(r.style[f])||yh(f,n)||((l=o==null?void 0:o.getValue(f))===null||l===void 0?void 0:l.liveStyle)!==void 0)&&(d[f]=u[f]);return d}function vh(n,r,o){const l=nu(n,r,o);for(const u in n)if(qe(n[u])||qe(r[u])){const d=Pr.indexOf(u)!==-1?"attr"+u.charAt(0).toUpperCase()+u.substring(1):u;l[d]=n[u]}return l}function u0(n,r){try{r.dimensions=typeof n.getBBox=="function"?n.getBBox():n.getBoundingClientRect()}catch{r.dimensions={x:0,y:0,width:0,height:0}}}const _d=["x","y","width","height","cx","cy","r"],c0={useVisualState:uh({scrapeMotionValuesFromProps:vh,createRenderState:ph,onUpdate:({props:n,prevProps:r,current:o,renderState:l,latestValues:u})=>{if(!o)return;let d=!!n.drag;if(!d){for(const p in u)if(Un.has(p)){d=!0;break}}if(!d)return;let f=!r;if(r)for(let p=0;p<_d.length;p++){const m=_d[p];n[m]!==r[m]&&(f=!0)}f&&Ce.read(()=>{u0(o,l),Ce.render(()=>{Ja(l,u,tu(o.tagName),n.transformTemplate),gh(o,l)})})}})},f0={useVisualState:uh({scrapeMotionValuesFromProps:nu,createRenderState:eu})};function xh(n,r,o){for(const l in r)!qe(r[l])&&!yh(l,o)&&(n[l]=r[l])}function d0({transformTemplate:n},r){return b.useMemo(()=>{const o=eu();return qa(o,r,n),Object.assign({},o.vars,o.style)},[r])}function p0(n,r){const o=n.style||{},l={};return xh(l,o,n),Object.assign(l,d0(n,r)),l}function h0(n,r){const o={},l=p0(n,r);return n.drag&&n.dragListener!==!1&&(o.draggable=!1,l.userSelect=l.WebkitUserSelect=l.WebkitTouchCallout="none",l.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(o.tabIndex=0),o.style=l,o}function m0(n,r,o,l){const u=b.useMemo(()=>{const d=ph();return Ja(d,r,tu(l),n.transformTemplate),{...d.attrs,style:{...d.style}}},[r]);if(n.style){const d={};xh(d,n.style,n),u.style={...d,...u.style}}return u}function g0(n=!1){return(o,l,u,{latestValues:d},f)=>{const m=(Qa(o)?m0:h0)(l,d,f,o),g=Dv(l,typeof o=="string",n),y=o!==b.Fragment?{...g,...m,ref:u}:{},{children:x}=l,S=b.useMemo(()=>qe(x)?x.get():x,[x]);return b.createElement(o,{...y,children:S})}}function y0(n,r){return function(l,{forwardMotionProps:u}={forwardMotionProps:!1}){const f={...Qa(l)?c0:f0,preloadedFeatures:n,useRender:g0(u),createVisualElement:r,Component:l};return bv(f)}}function wh(n,r){if(!Array.isArray(r))return!1;const o=r.length;if(o!==n.length)return!1;for(let l=0;l<o;l++)if(r[l]!==n[l])return!1;return!0}function ls(n,r,o){const l=n.getProps();return Ya(l,r,o!==void 0?o:l.custom,n)}const v0=Wa(()=>window.ScrollTimeline!==void 0);class x0{constructor(r){this.stop=()=>this.runAll("stop"),this.animations=r.filter(Boolean)}get finished(){return Promise.all(this.animations.map(r=>"finished"in r?r.finished:r))}getAll(r){return this.animations[0][r]}setAll(r,o){for(let l=0;l<this.animations.length;l++)this.animations[l][r]=o}attachTimeline(r,o){const l=this.animations.map(u=>{if(v0()&&u.attachTimeline)return u.attachTimeline(r);if(typeof o=="function")return o(u)});return()=>{l.forEach((u,d)=>{u&&u(),this.animations[d].stop()})}}get time(){return this.getAll("time")}set time(r){this.setAll("time",r)}get speed(){return this.getAll("speed")}set speed(r){this.setAll("speed",r)}get startTime(){return this.getAll("startTime")}get duration(){let r=0;for(let o=0;o<this.animations.length;o++)r=Math.max(r,this.animations[o].duration);return r}runAll(r){this.animations.forEach(o=>o[r]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class w0 extends x0{then(r,o){return Promise.all(this.animations).then(r).catch(o)}}function ru(n,r){return n?n[r]||n.default||n:void 0}const Pa=2e4;function Sh(n){let r=0;const o=50;let l=n.next(r);for(;!l.done&&r<Pa;)r+=o,l=n.next(r);return r>=Pa?1/0:r}function iu(n){return typeof n=="function"}function jd(n,r){n.timeline=r,n.onfinish=null}const ou=n=>Array.isArray(n)&&typeof n[0]=="number",S0={linearEasing:void 0};function k0(n,r){const o=Wa(n);return()=>{var l;return(l=S0[r])!==null&&l!==void 0?l:o()}}const qo=k0(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),kh=(n,r,o=10)=>{let l="";const u=Math.max(Math.round(r/o),2);for(let d=0;d<u;d++)l+=n(wr(0,u-1,d))+", ";return`linear(${l.substring(0,l.length-2)})`};function Ch(n){return!!(typeof n=="function"&&qo()||!n||typeof n=="string"&&(n in Ta||qo())||ou(n)||Array.isArray(n)&&n.every(Ch))}const mi=([n,r,o,l])=>`cubic-bezier(${n}, ${r}, ${o}, ${l})`,Ta={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:mi([0,.65,.55,1]),circOut:mi([.55,0,1,.45]),backIn:mi([.31,.01,.66,-.59]),backOut:mi([.33,1.53,.69,.99])};function Ph(n,r){if(n)return typeof n=="function"&&qo()?kh(n,r):ou(n)?mi(n):Array.isArray(n)?n.map(o=>Ph(o,r)||Ta.easeOut):Ta[n]}const Vt={x:!1,y:!1};function Th(){return Vt.x||Vt.y}function C0(n,r,o){var l;if(n instanceof Element)return[n];if(typeof n=="string"){let u=document;const d=(l=void 0)!==null&&l!==void 0?l:u.querySelectorAll(n);return d?Array.from(d):[]}return Array.from(n)}function Eh(n,r){const o=C0(n),l=new AbortController,u={passive:!0,...r,signal:l.signal};return[o,u,()=>l.abort()]}function Id(n){return r=>{r.pointerType==="touch"||Th()||n(r)}}function P0(n,r,o={}){const[l,u,d]=Eh(n,o),f=Id(p=>{const{target:m}=p,g=r(p);if(typeof g!="function"||!m)return;const y=Id(x=>{g(x),m.removeEventListener("pointerleave",y)});m.addEventListener("pointerleave",y,u)});return l.forEach(p=>{p.addEventListener("pointerenter",f,u)}),d}const Ah=(n,r)=>r?n===r?!0:Ah(n,r.parentElement):!1,su=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,T0=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function E0(n){return T0.has(n.tagName)||n.tabIndex!==-1}const gi=new WeakSet;function Od(n){return r=>{r.key==="Enter"&&n(r)}}function ua(n,r){n.dispatchEvent(new PointerEvent("pointer"+r,{isPrimary:!0,bubbles:!0}))}const A0=(n,r)=>{const o=n.currentTarget;if(!o)return;const l=Od(()=>{if(gi.has(o))return;ua(o,"down");const u=Od(()=>{ua(o,"up")}),d=()=>ua(o,"cancel");o.addEventListener("keyup",u,r),o.addEventListener("blur",d,r)});o.addEventListener("keydown",l,r),o.addEventListener("blur",()=>o.removeEventListener("keydown",l),r)};function zd(n){return su(n)&&!Th()}function M0(n,r,o={}){const[l,u,d]=Eh(n,o),f=p=>{const m=p.currentTarget;if(!zd(p)||gi.has(m))return;gi.add(m);const g=r(p),y=(A,N)=>{window.removeEventListener("pointerup",x),window.removeEventListener("pointercancel",S),!(!zd(A)||!gi.has(m))&&(gi.delete(m),typeof g=="function"&&g(A,{success:N}))},x=A=>{y(A,o.useGlobalTarget||Ah(m,A.target))},S=A=>{y(A,!1)};window.addEventListener("pointerup",x,u),window.addEventListener("pointercancel",S,u)};return l.forEach(p=>{!E0(p)&&p.getAttribute("tabindex")===null&&(p.tabIndex=0),(o.useGlobalTarget?window:p).addEventListener("pointerdown",f,u),p.addEventListener("focus",g=>A0(g,u),u)}),d}function R0(n){return n==="x"||n==="y"?Vt[n]?null:(Vt[n]=!0,()=>{Vt[n]=!1}):Vt.x||Vt.y?null:(Vt.x=Vt.y=!0,()=>{Vt.x=Vt.y=!1})}const Mh=new Set(["width","height","top","left","right","bottom",...Pr]);let Go;function N0(){Go=void 0}const Ft={now:()=>(Go===void 0&&Ft.set(Ke.isProcessing||Tv.useManualTiming?Ke.timestamp:performance.now()),Go),set:n=>{Go=n,queueMicrotask(N0)}};function lu(n,r){n.indexOf(r)===-1&&n.push(r)}function au(n,r){const o=n.indexOf(r);o>-1&&n.splice(o,1)}class uu{constructor(){this.subscriptions=[]}add(r){return lu(this.subscriptions,r),()=>au(this.subscriptions,r)}notify(r,o,l){const u=this.subscriptions.length;if(u)if(u===1)this.subscriptions[0](r,o,l);else for(let d=0;d<u;d++){const f=this.subscriptions[d];f&&f(r,o,l)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Rh(n,r){return r?n*(1e3/r):0}const Fd=30,D0=n=>!isNaN(parseFloat(n));class V0{constructor(r,o={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(l,u=!0)=>{const d=Ft.now();this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(l),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),u&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(r),this.owner=o.owner}setCurrent(r){this.current=r,this.updatedAt=Ft.now(),this.canTrackVelocity===null&&r!==void 0&&(this.canTrackVelocity=D0(this.current))}setPrevFrameValue(r=this.current){this.prevFrameValue=r,this.prevUpdatedAt=this.updatedAt}onChange(r){return this.on("change",r)}on(r,o){this.events[r]||(this.events[r]=new uu);const l=this.events[r].add(o);return r==="change"?()=>{l(),Ce.read(()=>{this.events.change.getSize()||this.stop()})}:l}clearListeners(){for(const r in this.events)this.events[r].clear()}attach(r,o){this.passiveEffect=r,this.stopPassiveEffect=o}set(r,o=!0){!o||!this.passiveEffect?this.updateAndNotify(r,o):this.passiveEffect(r,this.updateAndNotify)}setWithVelocity(r,o,l){this.set(o),this.prev=void 0,this.prevFrameValue=r,this.prevUpdatedAt=this.updatedAt-l}jump(r,o=!0){this.updateAndNotify(r),this.prev=r,this.prevUpdatedAt=this.prevFrameValue=void 0,o&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const r=Ft.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||r-this.updatedAt>Fd)return 0;const o=Math.min(this.updatedAt-this.prevUpdatedAt,Fd);return Rh(parseFloat(this.current)-parseFloat(this.prevFrameValue),o)}start(r){return this.stop(),new Promise(o=>{this.hasAnimated=!0,this.animation=r(o),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Pi(n,r){return new V0(n,r)}function L0(n,r,o){n.hasValue(r)?n.getValue(r).set(o):n.addValue(r,Pi(o))}function _0(n,r){const o=ls(n,r);let{transitionEnd:l={},transition:u={},...d}=o||{};d={...d,...l};for(const f in d){const p=Kv(d[f]);L0(n,f,p)}}function j0(n){return!!(qe(n)&&n.add)}function Ea(n,r){const o=n.getValue("willChange");if(j0(o))return o.add(r)}function Nh(n){return n.props[sh]}const Dh=(n,r,o)=>(((1-3*o+3*r)*n+(3*o-6*r))*n+3*r)*n,I0=1e-7,O0=12;function z0(n,r,o,l,u){let d,f,p=0;do f=r+(o-r)/2,d=Dh(f,l,u)-n,d>0?o=f:r=f;while(Math.abs(d)>I0&&++p<O0);return f}function Mi(n,r,o,l){if(n===r&&o===l)return pt;const u=d=>z0(d,0,1,n,o);return d=>d===0||d===1?d:Dh(u(d),r,l)}const Vh=n=>r=>r<=.5?n(2*r)/2:(2-n(2*(1-r)))/2,Lh=n=>r=>1-n(1-r),_h=Mi(.33,1.53,.69,.99),cu=Lh(_h),jh=Vh(cu),Ih=n=>(n*=2)<1?.5*cu(n):.5*(2-Math.pow(2,-10*(n-1))),fu=n=>1-Math.sin(Math.acos(n)),Oh=Lh(fu),zh=Vh(fu),Fh=n=>/^0[^.\s]+$/u.test(n);function F0(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||Fh(n):!0}const vi=n=>Math.round(n*1e5)/1e5,du=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function b0(n){return n==null}const B0=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,pu=(n,r)=>o=>!!(typeof o=="string"&&B0.test(o)&&o.startsWith(n)||r&&!b0(o)&&Object.prototype.hasOwnProperty.call(o,r)),bh=(n,r,o)=>l=>{if(typeof l!="string")return l;const[u,d,f,p]=l.match(du);return{[n]:parseFloat(u),[r]:parseFloat(d),[o]:parseFloat(f),alpha:p!==void 0?parseFloat(p):1}},U0=n=>Zt(0,255,n),ca={...Tr,transform:n=>Math.round(U0(n))},bn={test:pu("rgb","red"),parse:bh("red","green","blue"),transform:({red:n,green:r,blue:o,alpha:l=1})=>"rgba("+ca.transform(n)+", "+ca.transform(r)+", "+ca.transform(o)+", "+vi(Ci.transform(l))+")"};function W0(n){let r="",o="",l="",u="";return n.length>5?(r=n.substring(1,3),o=n.substring(3,5),l=n.substring(5,7),u=n.substring(7,9)):(r=n.substring(1,2),o=n.substring(2,3),l=n.substring(3,4),u=n.substring(4,5),r+=r,o+=o,l+=l,u+=u),{red:parseInt(r,16),green:parseInt(o,16),blue:parseInt(l,16),alpha:u?parseInt(u,16)/255:1}}const Aa={test:pu("#"),parse:W0,transform:bn.transform},mr={test:pu("hsl","hue"),parse:bh("hue","saturation","lightness"),transform:({hue:n,saturation:r,lightness:o,alpha:l=1})=>"hsla("+Math.round(n)+", "+zt.transform(vi(r))+", "+zt.transform(vi(o))+", "+vi(Ci.transform(l))+")"},Ze={test:n=>bn.test(n)||Aa.test(n)||mr.test(n),parse:n=>bn.test(n)?bn.parse(n):mr.test(n)?mr.parse(n):Aa.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?bn.transform(n):mr.transform(n)},$0=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function H0(n){var r,o;return isNaN(n)&&typeof n=="string"&&(((r=n.match(du))===null||r===void 0?void 0:r.length)||0)+(((o=n.match($0))===null||o===void 0?void 0:o.length)||0)>0}const Bh="number",Uh="color",K0="var",G0="var(",bd="${}",Q0=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ti(n){const r=n.toString(),o=[],l={color:[],number:[],var:[]},u=[];let d=0;const p=r.replace(Q0,m=>(Ze.test(m)?(l.color.push(d),u.push(Uh),o.push(Ze.parse(m))):m.startsWith(G0)?(l.var.push(d),u.push(K0),o.push(m)):(l.number.push(d),u.push(Bh),o.push(parseFloat(m))),++d,bd)).split(bd);return{values:o,split:p,indexes:l,types:u}}function Wh(n){return Ti(n).values}function $h(n){const{split:r,types:o}=Ti(n),l=r.length;return u=>{let d="";for(let f=0;f<l;f++)if(d+=r[f],u[f]!==void 0){const p=o[f];p===Bh?d+=vi(u[f]):p===Uh?d+=Ze.transform(u[f]):d+=u[f]}return d}}const Y0=n=>typeof n=="number"?0:n;function X0(n){const r=Wh(n);return $h(n)(r.map(Y0))}const kn={test:H0,parse:Wh,createTransformer:$h,getAnimatableNone:X0},Z0=new Set(["brightness","contrast","saturate","opacity"]);function q0(n){const[r,o]=n.slice(0,-1).split("(");if(r==="drop-shadow")return n;const[l]=o.match(du)||[];if(!l)return n;const u=o.replace(l,"");let d=Z0.has(r)?1:0;return l!==o&&(d*=100),r+"("+d+u+")"}const J0=/\b([a-z-]*)\(.*?\)/gu,Ma={...kn,getAnimatableNone:n=>{const r=n.match(J0);return r?r.map(q0).join(" "):n}},e1={...Za,color:Ze,backgroundColor:Ze,outlineColor:Ze,fill:Ze,stroke:Ze,borderColor:Ze,borderTopColor:Ze,borderRightColor:Ze,borderBottomColor:Ze,borderLeftColor:Ze,filter:Ma,WebkitFilter:Ma},hu=n=>e1[n];function Hh(n,r){let o=hu(n);return o!==Ma&&(o=kn),o.getAnimatableNone?o.getAnimatableNone(r):void 0}const t1=new Set(["auto","none","0"]);function n1(n,r,o){let l=0,u;for(;l<n.length&&!u;){const d=n[l];typeof d=="string"&&!t1.has(d)&&Ti(d).values.length&&(u=n[l]),l++}if(u&&o)for(const d of r)n[d]=Hh(o,u)}const Bd=n=>n===Tr||n===te,Ud=(n,r)=>parseFloat(n.split(", ")[r]),Wd=(n,r)=>(o,{transform:l})=>{if(l==="none"||!l)return 0;const u=l.match(/^matrix3d\((.+)\)$/u);if(u)return Ud(u[1],r);{const d=l.match(/^matrix\((.+)\)$/u);return d?Ud(d[1],n):0}},r1=new Set(["x","y","z"]),i1=Pr.filter(n=>!r1.has(n));function o1(n){const r=[];return i1.forEach(o=>{const l=n.getValue(o);l!==void 0&&(r.push([o,l.get()]),l.set(o.startsWith("scale")?1:0))}),r}const kr={width:({x:n},{paddingLeft:r="0",paddingRight:o="0"})=>n.max-n.min-parseFloat(r)-parseFloat(o),height:({y:n},{paddingTop:r="0",paddingBottom:o="0"})=>n.max-n.min-parseFloat(r)-parseFloat(o),top:(n,{top:r})=>parseFloat(r),left:(n,{left:r})=>parseFloat(r),bottom:({y:n},{top:r})=>parseFloat(r)+(n.max-n.min),right:({x:n},{left:r})=>parseFloat(r)+(n.max-n.min),x:Wd(4,13),y:Wd(5,14)};kr.translateX=kr.x;kr.translateY=kr.y;const Bn=new Set;let Ra=!1,Na=!1;function Kh(){if(Na){const n=Array.from(Bn).filter(l=>l.needsMeasurement),r=new Set(n.map(l=>l.element)),o=new Map;r.forEach(l=>{const u=o1(l);u.length&&(o.set(l,u),l.render())}),n.forEach(l=>l.measureInitialState()),r.forEach(l=>{l.render();const u=o.get(l);u&&u.forEach(([d,f])=>{var p;(p=l.getValue(d))===null||p===void 0||p.set(f)})}),n.forEach(l=>l.measureEndState()),n.forEach(l=>{l.suspendedScrollY!==void 0&&window.scrollTo(0,l.suspendedScrollY)})}Na=!1,Ra=!1,Bn.forEach(n=>n.complete()),Bn.clear()}function Gh(){Bn.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(Na=!0)})}function s1(){Gh(),Kh()}class mu{constructor(r,o,l,u,d,f=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...r],this.onComplete=o,this.name=l,this.motionValue=u,this.element=d,this.isAsync=f}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Bn.add(this),Ra||(Ra=!0,Ce.read(Gh),Ce.resolveKeyframes(Kh))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:r,name:o,element:l,motionValue:u}=this;for(let d=0;d<r.length;d++)if(r[d]===null)if(d===0){const f=u==null?void 0:u.get(),p=r[r.length-1];if(f!==void 0)r[0]=f;else if(l&&o){const m=l.readValue(o,p);m!=null&&(r[0]=m)}r[0]===void 0&&(r[0]=p),u&&f===void 0&&u.set(r[0])}else r[d]=r[d-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Bn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Bn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Qh=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n),l1=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function a1(n){const r=l1.exec(n);if(!r)return[,];const[,o,l,u]=r;return[`--${o??l}`,u]}function Yh(n,r,o=1){const[l,u]=a1(n);if(!l)return;const d=window.getComputedStyle(r).getPropertyValue(l);if(d){const f=d.trim();return Qh(f)?parseFloat(f):f}return Xa(u)?Yh(u,r,o+1):u}const Xh=n=>r=>r.test(n),u1={test:n=>n==="auto",parse:n=>n},Zh=[Tr,te,zt,wn,qv,Zv,u1],$d=n=>Zh.find(Xh(n));class qh extends mu{constructor(r,o,l,u,d){super(r,o,l,u,d,!0)}readKeyframes(){const{unresolvedKeyframes:r,element:o,name:l}=this;if(!o||!o.current)return;super.readKeyframes();for(let m=0;m<r.length;m++){let g=r[m];if(typeof g=="string"&&(g=g.trim(),Xa(g))){const y=Yh(g,o.current);y!==void 0&&(r[m]=y),m===r.length-1&&(this.finalKeyframe=g)}}if(this.resolveNoneKeyframes(),!Mh.has(l)||r.length!==2)return;const[u,d]=r,f=$d(u),p=$d(d);if(f!==p)if(Bd(f)&&Bd(p))for(let m=0;m<r.length;m++){const g=r[m];typeof g=="string"&&(r[m]=parseFloat(g))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:r,name:o}=this,l=[];for(let u=0;u<r.length;u++)F0(r[u])&&l.push(u);l.length&&n1(r,l,o)}measureInitialState(){const{element:r,unresolvedKeyframes:o,name:l}=this;if(!r||!r.current)return;l==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=kr[l](r.measureViewportBox(),window.getComputedStyle(r.current)),o[0]=this.measuredOrigin;const u=o[o.length-1];u!==void 0&&r.getValue(l,u).jump(u,!1)}measureEndState(){var r;const{element:o,name:l,unresolvedKeyframes:u}=this;if(!o||!o.current)return;const d=o.getValue(l);d&&d.jump(this.measuredOrigin,!1);const f=u.length-1,p=u[f];u[f]=kr[l](o.measureViewportBox(),window.getComputedStyle(o.current)),p!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=p),!((r=this.removedTransforms)===null||r===void 0)&&r.length&&this.removedTransforms.forEach(([m,g])=>{o.getValue(m).set(g)}),this.resolveNoneKeyframes()}}const Hd=(n,r)=>r==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(kn.test(n)||n==="0")&&!n.startsWith("url("));function c1(n){const r=n[0];if(n.length===1)return!0;for(let o=0;o<n.length;o++)if(n[o]!==r)return!0}function f1(n,r,o,l){const u=n[0];if(u===null)return!1;if(r==="display"||r==="visibility")return!0;const d=n[n.length-1],f=Hd(u,r),p=Hd(d,r);return!f||!p?!1:c1(n)||(o==="spring"||iu(o))&&l}const d1=n=>n!==null;function as(n,{repeat:r,repeatType:o="loop"},l){const u=n.filter(d1),d=r&&o!=="loop"&&r%2===1?0:u.length-1;return!d||l===void 0?u[d]:l}const p1=40;class Jh{constructor({autoplay:r=!0,delay:o=0,type:l="keyframes",repeat:u=0,repeatDelay:d=0,repeatType:f="loop",...p}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Ft.now(),this.options={autoplay:r,delay:o,type:l,repeat:u,repeatDelay:d,repeatType:f,...p},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>p1?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&s1(),this._resolved}onKeyframesResolved(r,o){this.resolvedAt=Ft.now(),this.hasAttemptedResolve=!0;const{name:l,type:u,velocity:d,delay:f,onComplete:p,onUpdate:m,isGenerator:g}=this.options;if(!g&&!f1(r,l,u,d))if(f)this.options.duration=0;else{m&&m(as(r,this.options,o)),p&&p(),this.resolveFinishedPromise();return}const y=this.initPlayback(r,o);y!==!1&&(this._resolved={keyframes:r,finalKeyframe:o,...y},this.onPostResolved())}onPostResolved(){}then(r,o){return this.currentFinishedPromise.then(r,o)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(r=>{this.resolveFinishedPromise=r})}}const Me=(n,r,o)=>n+(r-n)*o;function fa(n,r,o){return o<0&&(o+=1),o>1&&(o-=1),o<1/6?n+(r-n)*6*o:o<1/2?r:o<2/3?n+(r-n)*(2/3-o)*6:n}function h1({hue:n,saturation:r,lightness:o,alpha:l}){n/=360,r/=100,o/=100;let u=0,d=0,f=0;if(!r)u=d=f=o;else{const p=o<.5?o*(1+r):o+r-o*r,m=2*o-p;u=fa(m,p,n+1/3),d=fa(m,p,n),f=fa(m,p,n-1/3)}return{red:Math.round(u*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:l}}function Jo(n,r){return o=>o>0?r:n}const da=(n,r,o)=>{const l=n*n,u=o*(r*r-l)+l;return u<0?0:Math.sqrt(u)},m1=[Aa,bn,mr],g1=n=>m1.find(r=>r.test(n));function Kd(n){const r=g1(n);if(!r)return!1;let o=r.parse(n);return r===mr&&(o=h1(o)),o}const Gd=(n,r)=>{const o=Kd(n),l=Kd(r);if(!o||!l)return Jo(n,r);const u={...o};return d=>(u.red=da(o.red,l.red,d),u.green=da(o.green,l.green,d),u.blue=da(o.blue,l.blue,d),u.alpha=Me(o.alpha,l.alpha,d),bn.transform(u))},y1=(n,r)=>o=>r(n(o)),Ri=(...n)=>n.reduce(y1),Da=new Set(["none","hidden"]);function v1(n,r){return Da.has(n)?o=>o<=0?n:r:o=>o>=1?r:n}function x1(n,r){return o=>Me(n,r,o)}function gu(n){return typeof n=="number"?x1:typeof n=="string"?Xa(n)?Jo:Ze.test(n)?Gd:k1:Array.isArray(n)?em:typeof n=="object"?Ze.test(n)?Gd:w1:Jo}function em(n,r){const o=[...n],l=o.length,u=n.map((d,f)=>gu(d)(d,r[f]));return d=>{for(let f=0;f<l;f++)o[f]=u[f](d);return o}}function w1(n,r){const o={...n,...r},l={};for(const u in o)n[u]!==void 0&&r[u]!==void 0&&(l[u]=gu(n[u])(n[u],r[u]));return u=>{for(const d in l)o[d]=l[d](u);return o}}function S1(n,r){var o;const l=[],u={color:0,var:0,number:0};for(let d=0;d<r.values.length;d++){const f=r.types[d],p=n.indexes[f][u[f]],m=(o=n.values[p])!==null&&o!==void 0?o:0;l[d]=m,u[f]++}return l}const k1=(n,r)=>{const o=kn.createTransformer(r),l=Ti(n),u=Ti(r);return l.indexes.var.length===u.indexes.var.length&&l.indexes.color.length===u.indexes.color.length&&l.indexes.number.length>=u.indexes.number.length?Da.has(n)&&!u.values.length||Da.has(r)&&!l.values.length?v1(n,r):Ri(em(S1(l,u),u.values),o):Jo(n,r)};function tm(n,r,o){return typeof n=="number"&&typeof r=="number"&&typeof o=="number"?Me(n,r,o):gu(n)(n,r)}const C1=5;function nm(n,r,o){const l=Math.max(r-C1,0);return Rh(o-n(l),r-l)}const Ne={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},pa=.001;function P1({duration:n=Ne.duration,bounce:r=Ne.bounce,velocity:o=Ne.velocity,mass:l=Ne.mass}){let u,d,f=1-r;f=Zt(Ne.minDamping,Ne.maxDamping,f),n=Zt(Ne.minDuration,Ne.maxDuration,Xt(n)),f<1?(u=g=>{const y=g*f,x=y*n,S=y-o,A=Va(g,f),N=Math.exp(-x);return pa-S/A*N},d=g=>{const x=g*f*n,S=x*o+o,A=Math.pow(f,2)*Math.pow(g,2)*n,N=Math.exp(-x),M=Va(Math.pow(g,2),f);return(-u(g)+pa>0?-1:1)*((S-A)*N)/M}):(u=g=>{const y=Math.exp(-g*n),x=(g-o)*n+1;return-pa+y*x},d=g=>{const y=Math.exp(-g*n),x=(o-g)*(n*n);return y*x});const p=5/n,m=E1(u,d,p);if(n=Yt(n),isNaN(m))return{stiffness:Ne.stiffness,damping:Ne.damping,duration:n};{const g=Math.pow(m,2)*l;return{stiffness:g,damping:f*2*Math.sqrt(l*g),duration:n}}}const T1=12;function E1(n,r,o){let l=o;for(let u=1;u<T1;u++)l=l-n(l)/r(l);return l}function Va(n,r){return n*Math.sqrt(1-r*r)}const A1=["duration","bounce"],M1=["stiffness","damping","mass"];function Qd(n,r){return r.some(o=>n[o]!==void 0)}function R1(n){let r={velocity:Ne.velocity,stiffness:Ne.stiffness,damping:Ne.damping,mass:Ne.mass,isResolvedFromDuration:!1,...n};if(!Qd(n,M1)&&Qd(n,A1))if(n.visualDuration){const o=n.visualDuration,l=2*Math.PI/(o*1.2),u=l*l,d=2*Zt(.05,1,1-(n.bounce||0))*Math.sqrt(u);r={...r,mass:Ne.mass,stiffness:u,damping:d}}else{const o=P1(n);r={...r,...o,mass:Ne.mass},r.isResolvedFromDuration=!0}return r}function rm(n=Ne.visualDuration,r=Ne.bounce){const o=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:r}:n;let{restSpeed:l,restDelta:u}=o;const d=o.keyframes[0],f=o.keyframes[o.keyframes.length-1],p={done:!1,value:d},{stiffness:m,damping:g,mass:y,duration:x,velocity:S,isResolvedFromDuration:A}=R1({...o,velocity:-Xt(o.velocity||0)}),N=S||0,M=g/(2*Math.sqrt(m*y)),R=f-d,_=Xt(Math.sqrt(m/y)),F=Math.abs(R)<5;l||(l=F?Ne.restSpeed.granular:Ne.restSpeed.default),u||(u=F?Ne.restDelta.granular:Ne.restDelta.default);let U;if(M<1){const G=Va(_,M);U=W=>{const ne=Math.exp(-M*_*W);return f-ne*((N+M*_*R)/G*Math.sin(G*W)+R*Math.cos(G*W))}}else if(M===1)U=G=>f-Math.exp(-_*G)*(R+(N+_*R)*G);else{const G=_*Math.sqrt(M*M-1);U=W=>{const ne=Math.exp(-M*_*W),X=Math.min(G*W,300);return f-ne*((N+M*_*R)*Math.sinh(X)+G*R*Math.cosh(X))/G}}const q={calculatedDuration:A&&x||null,next:G=>{const W=U(G);if(A)p.done=G>=x;else{let ne=0;M<1&&(ne=G===0?Yt(N):nm(U,G,W));const X=Math.abs(ne)<=l,ge=Math.abs(f-W)<=u;p.done=X&&ge}return p.value=p.done?f:W,p},toString:()=>{const G=Math.min(Sh(q),Pa),W=kh(ne=>q.next(G*ne).value,G,30);return G+"ms "+W}};return q}function Yd({keyframes:n,velocity:r=0,power:o=.8,timeConstant:l=325,bounceDamping:u=10,bounceStiffness:d=500,modifyTarget:f,min:p,max:m,restDelta:g=.5,restSpeed:y}){const x=n[0],S={done:!1,value:x},A=X=>p!==void 0&&X<p||m!==void 0&&X>m,N=X=>p===void 0?m:m===void 0||Math.abs(p-X)<Math.abs(m-X)?p:m;let M=o*r;const R=x+M,_=f===void 0?R:f(R);_!==R&&(M=_-x);const F=X=>-M*Math.exp(-X/l),U=X=>_+F(X),q=X=>{const ge=F(X),ye=U(X);S.done=Math.abs(ge)<=g,S.value=S.done?_:ye};let G,W;const ne=X=>{A(S.value)&&(G=X,W=rm({keyframes:[S.value,N(S.value)],velocity:nm(U,X,S.value),damping:u,stiffness:d,restDelta:g,restSpeed:y}))};return ne(0),{calculatedDuration:null,next:X=>{let ge=!1;return!W&&G===void 0&&(ge=!0,q(X),ne(X)),G!==void 0&&X>=G?W.next(X-G):(!ge&&q(X),S)}}}const N1=Mi(.42,0,1,1),D1=Mi(0,0,.58,1),im=Mi(.42,0,.58,1),V1=n=>Array.isArray(n)&&typeof n[0]!="number",L1={linear:pt,easeIn:N1,easeInOut:im,easeOut:D1,circIn:fu,circInOut:zh,circOut:Oh,backIn:cu,backInOut:jh,backOut:_h,anticipate:Ih},Xd=n=>{if(ou(n)){th(n.length===4);const[r,o,l,u]=n;return Mi(r,o,l,u)}else if(typeof n=="string")return L1[n];return n};function _1(n,r,o){const l=[],u=o||tm,d=n.length-1;for(let f=0;f<d;f++){let p=u(n[f],n[f+1]);if(r){const m=Array.isArray(r)?r[f]||pt:r;p=Ri(m,p)}l.push(p)}return l}function j1(n,r,{clamp:o=!0,ease:l,mixer:u}={}){const d=n.length;if(th(d===r.length),d===1)return()=>r[0];if(d===2&&r[0]===r[1])return()=>r[1];const f=n[0]===n[1];n[0]>n[d-1]&&(n=[...n].reverse(),r=[...r].reverse());const p=_1(r,l,u),m=p.length,g=y=>{if(f&&y<n[0])return r[0];let x=0;if(m>1)for(;x<n.length-2&&!(y<n[x+1]);x++);const S=wr(n[x],n[x+1],y);return p[x](S)};return o?y=>g(Zt(n[0],n[d-1],y)):g}function I1(n,r){const o=n[n.length-1];for(let l=1;l<=r;l++){const u=wr(0,r,l);n.push(Me(o,1,u))}}function O1(n){const r=[0];return I1(r,n.length-1),r}function z1(n,r){return n.map(o=>o*r)}function F1(n,r){return n.map(()=>r||im).splice(0,n.length-1)}function es({duration:n=300,keyframes:r,times:o,ease:l="easeInOut"}){const u=V1(l)?l.map(Xd):Xd(l),d={done:!1,value:r[0]},f=z1(o&&o.length===r.length?o:O1(r),n),p=j1(f,r,{ease:Array.isArray(u)?u:F1(r,u)});return{calculatedDuration:n,next:m=>(d.value=p(m),d.done=m>=n,d)}}const b1=n=>{const r=({timestamp:o})=>n(o);return{start:()=>Ce.update(r,!0),stop:()=>Sn(r),now:()=>Ke.isProcessing?Ke.timestamp:Ft.now()}},B1={decay:Yd,inertia:Yd,tween:es,keyframes:es,spring:rm},U1=n=>n/100;class yu extends Jh{constructor(r){super(r),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:m}=this.options;m&&m()};const{name:o,motionValue:l,element:u,keyframes:d}=this.options,f=(u==null?void 0:u.KeyframeResolver)||mu,p=(m,g)=>this.onKeyframesResolved(m,g);this.resolver=new f(d,p,o,l,u),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(r){const{type:o="keyframes",repeat:l=0,repeatDelay:u=0,repeatType:d,velocity:f=0}=this.options,p=iu(o)?o:B1[o]||es;let m,g;p!==es&&typeof r[0]!="number"&&(m=Ri(U1,tm(r[0],r[1])),r=[0,100]);const y=p({...this.options,keyframes:r});d==="mirror"&&(g=p({...this.options,keyframes:[...r].reverse(),velocity:-f})),y.calculatedDuration===null&&(y.calculatedDuration=Sh(y));const{calculatedDuration:x}=y,S=x+u,A=S*(l+1)-u;return{generator:y,mirroredGenerator:g,mapPercentToKeyframes:m,calculatedDuration:x,resolvedDuration:S,totalDuration:A}}onPostResolved(){const{autoplay:r=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!r?this.pause():this.state=this.pendingPlayState}tick(r,o=!1){const{resolved:l}=this;if(!l){const{keyframes:X}=this.options;return{done:!0,value:X[X.length-1]}}const{finalKeyframe:u,generator:d,mirroredGenerator:f,mapPercentToKeyframes:p,keyframes:m,calculatedDuration:g,totalDuration:y,resolvedDuration:x}=l;if(this.startTime===null)return d.next(0);const{delay:S,repeat:A,repeatType:N,repeatDelay:M,onUpdate:R}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,r):this.speed<0&&(this.startTime=Math.min(r-y/this.speed,this.startTime)),o?this.currentTime=r:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(r-this.startTime)*this.speed;const _=this.currentTime-S*(this.speed>=0?1:-1),F=this.speed>=0?_<0:_>y;this.currentTime=Math.max(_,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=y);let U=this.currentTime,q=d;if(A){const X=Math.min(this.currentTime,y)/x;let ge=Math.floor(X),ye=X%1;!ye&&X>=1&&(ye=1),ye===1&&ge--,ge=Math.min(ge,A+1),!!(ge%2)&&(N==="reverse"?(ye=1-ye,M&&(ye-=M/x)):N==="mirror"&&(q=f)),U=Zt(0,1,ye)*x}const G=F?{done:!1,value:m[0]}:q.next(U);p&&(G.value=p(G.value));let{done:W}=G;!F&&g!==null&&(W=this.speed>=0?this.currentTime>=y:this.currentTime<=0);const ne=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&W);return ne&&u!==void 0&&(G.value=as(m,this.options,u)),R&&R(G.value),ne&&this.finish(),G}get duration(){const{resolved:r}=this;return r?Xt(r.calculatedDuration):0}get time(){return Xt(this.currentTime)}set time(r){r=Yt(r),this.currentTime=r,this.holdTime!==null||this.speed===0?this.holdTime=r:this.driver&&(this.startTime=this.driver.now()-r/this.speed)}get speed(){return this.playbackSpeed}set speed(r){const o=this.playbackSpeed!==r;this.playbackSpeed=r,o&&(this.time=Xt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:r=b1,onPlay:o,startTime:l}=this.options;this.driver||(this.driver=r(d=>this.tick(d))),o&&o();const u=this.driver.now();this.holdTime!==null?this.startTime=u-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=u):this.startTime=l??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var r;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(r=this.currentTime)!==null&&r!==void 0?r:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:r}=this.options;r&&r()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(r){return this.startTime=0,this.tick(r,!0)}}const W1=new Set(["opacity","clipPath","filter","transform"]);function $1(n,r,o,{delay:l=0,duration:u=300,repeat:d=0,repeatType:f="loop",ease:p="easeInOut",times:m}={}){const g={[r]:o};m&&(g.offset=m);const y=Ph(p,u);return Array.isArray(y)&&(g.easing=y),n.animate(g,{delay:l,duration:u,easing:Array.isArray(y)?"linear":y,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"})}const H1=Wa(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ts=10,K1=2e4;function G1(n){return iu(n.type)||n.type==="spring"||!Ch(n.ease)}function Q1(n,r){const o=new yu({...r,keyframes:n,repeat:0,delay:0,isGenerator:!0});let l={done:!1,value:n[0]};const u=[];let d=0;for(;!l.done&&d<K1;)l=o.sample(d),u.push(l.value),d+=ts;return{times:void 0,keyframes:u,duration:d-ts,ease:"linear"}}const om={anticipate:Ih,backInOut:jh,circInOut:zh};function Y1(n){return n in om}class Zd extends Jh{constructor(r){super(r);const{name:o,motionValue:l,element:u,keyframes:d}=this.options;this.resolver=new qh(d,(f,p)=>this.onKeyframesResolved(f,p),o,l,u),this.resolver.scheduleResolve()}initPlayback(r,o){let{duration:l=300,times:u,ease:d,type:f,motionValue:p,name:m,startTime:g}=this.options;if(!p.owner||!p.owner.current)return!1;if(typeof d=="string"&&qo()&&Y1(d)&&(d=om[d]),G1(this.options)){const{onComplete:x,onUpdate:S,motionValue:A,element:N,...M}=this.options,R=Q1(r,M);r=R.keyframes,r.length===1&&(r[1]=r[0]),l=R.duration,u=R.times,d=R.ease,f="keyframes"}const y=$1(p.owner.current,m,r,{...this.options,duration:l,times:u,ease:d});return y.startTime=g??this.calcStartTime(),this.pendingTimeline?(jd(y,this.pendingTimeline),this.pendingTimeline=void 0):y.onfinish=()=>{const{onComplete:x}=this.options;p.set(as(r,this.options,o)),x&&x(),this.cancel(),this.resolveFinishedPromise()},{animation:y,duration:l,times:u,type:f,ease:d,keyframes:r}}get duration(){const{resolved:r}=this;if(!r)return 0;const{duration:o}=r;return Xt(o)}get time(){const{resolved:r}=this;if(!r)return 0;const{animation:o}=r;return Xt(o.currentTime||0)}set time(r){const{resolved:o}=this;if(!o)return;const{animation:l}=o;l.currentTime=Yt(r)}get speed(){const{resolved:r}=this;if(!r)return 1;const{animation:o}=r;return o.playbackRate}set speed(r){const{resolved:o}=this;if(!o)return;const{animation:l}=o;l.playbackRate=r}get state(){const{resolved:r}=this;if(!r)return"idle";const{animation:o}=r;return o.playState}get startTime(){const{resolved:r}=this;if(!r)return null;const{animation:o}=r;return o.startTime}attachTimeline(r){if(!this._resolved)this.pendingTimeline=r;else{const{resolved:o}=this;if(!o)return pt;const{animation:l}=o;jd(l,r)}return pt}play(){if(this.isStopped)return;const{resolved:r}=this;if(!r)return;const{animation:o}=r;o.playState==="finished"&&this.updateFinishedPromise(),o.play()}pause(){const{resolved:r}=this;if(!r)return;const{animation:o}=r;o.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:r}=this;if(!r)return;const{animation:o,keyframes:l,duration:u,type:d,ease:f,times:p}=r;if(o.playState==="idle"||o.playState==="finished")return;if(this.time){const{motionValue:g,onUpdate:y,onComplete:x,element:S,...A}=this.options,N=new yu({...A,keyframes:l,duration:u,type:d,ease:f,times:p,isGenerator:!0}),M=Yt(this.time);g.setWithVelocity(N.sample(M-ts).value,N.sample(M).value,ts)}const{onStop:m}=this.options;m&&m(),this.cancel()}complete(){const{resolved:r}=this;r&&r.animation.finish()}cancel(){const{resolved:r}=this;r&&r.animation.cancel()}static supports(r){const{motionValue:o,name:l,repeatDelay:u,repeatType:d,damping:f,type:p}=r;if(!o||!o.owner||!(o.owner.current instanceof HTMLElement))return!1;const{onUpdate:m,transformTemplate:g}=o.owner.getProps();return H1()&&l&&W1.has(l)&&!m&&!g&&!u&&d!=="mirror"&&f!==0&&p!=="inertia"}}const X1={type:"spring",stiffness:500,damping:25,restSpeed:10},Z1=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),q1={type:"keyframes",duration:.8},J1={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ex=(n,{keyframes:r})=>r.length>2?q1:Un.has(n)?n.startsWith("scale")?Z1(r[1]):X1:J1;function tx({when:n,delay:r,delayChildren:o,staggerChildren:l,staggerDirection:u,repeat:d,repeatType:f,repeatDelay:p,from:m,elapsed:g,...y}){return!!Object.keys(y).length}const vu=(n,r,o,l={},u,d)=>f=>{const p=ru(l,n)||{},m=p.delay||l.delay||0;let{elapsed:g=0}=l;g=g-Yt(m);let y={keyframes:Array.isArray(o)?o:[null,o],ease:"easeOut",velocity:r.getVelocity(),...p,delay:-g,onUpdate:S=>{r.set(S),p.onUpdate&&p.onUpdate(S)},onComplete:()=>{f(),p.onComplete&&p.onComplete()},name:n,motionValue:r,element:d?void 0:u};tx(p)||(y={...y,...ex(n,y)}),y.duration&&(y.duration=Yt(y.duration)),y.repeatDelay&&(y.repeatDelay=Yt(y.repeatDelay)),y.from!==void 0&&(y.keyframes[0]=y.from);let x=!1;if((y.type===!1||y.duration===0&&!y.repeatDelay)&&(y.duration=0,y.delay===0&&(x=!0)),x&&!d&&r.get()!==void 0){const S=as(y.keyframes,p);if(S!==void 0)return Ce.update(()=>{y.onUpdate(S),y.onComplete()}),new w0([])}return!d&&Zd.supports(y)?new Zd(y):new yu(y)};function nx({protectedKeys:n,needsAnimating:r},o){const l=n.hasOwnProperty(o)&&r[o]!==!0;return r[o]=!1,l}function sm(n,r,{delay:o=0,transitionOverride:l,type:u}={}){var d;let{transition:f=n.getDefaultTransition(),transitionEnd:p,...m}=r;l&&(f=l);const g=[],y=u&&n.animationState&&n.animationState.getState()[u];for(const x in m){const S=n.getValue(x,(d=n.latestValues[x])!==null&&d!==void 0?d:null),A=m[x];if(A===void 0||y&&nx(y,x))continue;const N={delay:o,...ru(f||{},x)};let M=!1;if(window.MotionHandoffAnimation){const _=Nh(n);if(_){const F=window.MotionHandoffAnimation(_,x,Ce);F!==null&&(N.startTime=F,M=!0)}}Ea(n,x),S.start(vu(x,S,A,n.shouldReduceMotion&&Mh.has(x)?{type:!1}:N,n,M));const R=S.animation;R&&g.push(R)}return p&&Promise.all(g).then(()=>{Ce.update(()=>{p&&_0(n,p)})}),g}function La(n,r,o={}){var l;const u=ls(n,r,o.type==="exit"?(l=n.presenceContext)===null||l===void 0?void 0:l.custom:void 0);let{transition:d=n.getDefaultTransition()||{}}=u||{};o.transitionOverride&&(d=o.transitionOverride);const f=u?()=>Promise.all(sm(n,u,o)):()=>Promise.resolve(),p=n.variantChildren&&n.variantChildren.size?(g=0)=>{const{delayChildren:y=0,staggerChildren:x,staggerDirection:S}=d;return rx(n,r,y+g,x,S,o)}:()=>Promise.resolve(),{when:m}=d;if(m){const[g,y]=m==="beforeChildren"?[f,p]:[p,f];return g().then(()=>y())}else return Promise.all([f(),p(o.delay)])}function rx(n,r,o=0,l=0,u=1,d){const f=[],p=(n.variantChildren.size-1)*l,m=u===1?(g=0)=>g*l:(g=0)=>p-g*l;return Array.from(n.variantChildren).sort(ix).forEach((g,y)=>{g.notify("AnimationStart",r),f.push(La(g,r,{...d,delay:o+m(y)}).then(()=>g.notify("AnimationComplete",r)))}),Promise.all(f)}function ix(n,r){return n.sortNodePosition(r)}function ox(n,r,o={}){n.notify("AnimationStart",r);let l;if(Array.isArray(r)){const u=r.map(d=>La(n,d,o));l=Promise.all(u)}else if(typeof r=="string")l=La(n,r,o);else{const u=typeof r=="function"?ls(n,r,o.custom):r;l=Promise.all(sm(n,u,o))}return l.then(()=>{n.notify("AnimationComplete",r)})}const sx=Ha.length;function lm(n){if(!n)return;if(!n.isControllingVariants){const o=n.parent?lm(n.parent)||{}:{};return n.props.initial!==void 0&&(o.initial=n.props.initial),o}const r={};for(let o=0;o<sx;o++){const l=Ha[o],u=n.props[l];(ki(u)||u===!1)&&(r[l]=u)}return r}const lx=[...$a].reverse(),ax=$a.length;function ux(n){return r=>Promise.all(r.map(({animation:o,options:l})=>ox(n,o,l)))}function cx(n){let r=ux(n),o=qd(),l=!0;const u=m=>(g,y)=>{var x;const S=ls(n,y,m==="exit"?(x=n.presenceContext)===null||x===void 0?void 0:x.custom:void 0);if(S){const{transition:A,transitionEnd:N,...M}=S;g={...g,...M,...N}}return g};function d(m){r=m(n)}function f(m){const{props:g}=n,y=lm(n.parent)||{},x=[],S=new Set;let A={},N=1/0;for(let R=0;R<ax;R++){const _=lx[R],F=o[_],U=g[_]!==void 0?g[_]:y[_],q=ki(U),G=_===m?F.isActive:null;G===!1&&(N=R);let W=U===y[_]&&U!==g[_]&&q;if(W&&l&&n.manuallyAnimateOnMount&&(W=!1),F.protectedKeys={...A},!F.isActive&&G===null||!U&&!F.prevProp||os(U)||typeof U=="boolean")continue;const ne=fx(F.prevProp,U);let X=ne||_===m&&F.isActive&&!W&&q||R>N&&q,ge=!1;const ye=Array.isArray(U)?U:[U];let Fe=ye.reduce(u(_),{});G===!1&&(Fe={});const{prevResolvedValues:le={}}=F,be={...le,...Fe},Ie=ie=>{X=!0,S.has(ie)&&(ge=!0,S.delete(ie)),F.needsAnimating[ie]=!0;const z=n.getValue(ie);z&&(z.liveStyle=!1)};for(const ie in be){const z=Fe[ie],Z=le[ie];if(A.hasOwnProperty(ie))continue;let B=!1;Ca(z)&&Ca(Z)?B=!wh(z,Z):B=z!==Z,B?z!=null?Ie(ie):S.add(ie):z!==void 0&&S.has(ie)?Ie(ie):F.protectedKeys[ie]=!0}F.prevProp=U,F.prevResolvedValues=Fe,F.isActive&&(A={...A,...Fe}),l&&n.blockInitialAnimation&&(X=!1),X&&(!(W&&ne)||ge)&&x.push(...ye.map(ie=>({animation:ie,options:{type:_}})))}if(S.size){const R={};S.forEach(_=>{const F=n.getBaseTarget(_),U=n.getValue(_);U&&(U.liveStyle=!0),R[_]=F??null}),x.push({animation:R})}let M=!!x.length;return l&&(g.initial===!1||g.initial===g.animate)&&!n.manuallyAnimateOnMount&&(M=!1),l=!1,M?r(x):Promise.resolve()}function p(m,g){var y;if(o[m].isActive===g)return Promise.resolve();(y=n.variantChildren)===null||y===void 0||y.forEach(S=>{var A;return(A=S.animationState)===null||A===void 0?void 0:A.setActive(m,g)}),o[m].isActive=g;const x=f(m);for(const S in o)o[S].protectedKeys={};return x}return{animateChanges:f,setActive:p,setAnimateFunction:d,getState:()=>o,reset:()=>{o=qd(),l=!0}}}function fx(n,r){return typeof r=="string"?r!==n:Array.isArray(r)?!wh(r,n):!1}function On(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function qd(){return{animate:On(!0),whileInView:On(),whileHover:On(),whileTap:On(),whileDrag:On(),whileFocus:On(),exit:On()}}class Cn{constructor(r){this.isMounted=!1,this.node=r}update(){}}class dx extends Cn{constructor(r){super(r),r.animationState||(r.animationState=cx(r))}updateAnimationControlsSubscription(){const{animate:r}=this.node.getProps();os(r)&&(this.unmountControls=r.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:r}=this.node.getProps(),{animate:o}=this.node.prevProps||{};r!==o&&this.updateAnimationControlsSubscription()}unmount(){var r;this.node.animationState.reset(),(r=this.unmountControls)===null||r===void 0||r.call(this)}}let px=0;class hx extends Cn{constructor(){super(...arguments),this.id=px++}update(){if(!this.node.presenceContext)return;const{isPresent:r,onExitComplete:o}=this.node.presenceContext,{isPresent:l}=this.node.prevPresenceContext||{};if(!this.node.animationState||r===l)return;const u=this.node.animationState.setActive("exit",!r);o&&!r&&u.then(()=>o(this.id))}mount(){const{register:r}=this.node.presenceContext||{};r&&(this.unmount=r(this.id))}unmount(){}}const mx={animation:{Feature:dx},exit:{Feature:hx}};function Ei(n,r,o,l={passive:!0}){return n.addEventListener(r,o,l),()=>n.removeEventListener(r,o)}function Ni(n){return{point:{x:n.pageX,y:n.pageY}}}const gx=n=>r=>su(r)&&n(r,Ni(r));function xi(n,r,o,l){return Ei(n,r,gx(o),l)}const Jd=(n,r)=>Math.abs(n-r);function yx(n,r){const o=Jd(n.x,r.x),l=Jd(n.y,r.y);return Math.sqrt(o**2+l**2)}class am{constructor(r,o,{transformPagePoint:l,contextWindow:u,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ma(this.lastMoveEventInfo,this.history),S=this.startEvent!==null,A=yx(x.offset,{x:0,y:0})>=3;if(!S&&!A)return;const{point:N}=x,{timestamp:M}=Ke;this.history.push({...N,timestamp:M});const{onStart:R,onMove:_}=this.handlers;S||(R&&R(this.lastMoveEvent,x),this.startEvent=this.lastMoveEvent),_&&_(this.lastMoveEvent,x)},this.handlePointerMove=(x,S)=>{this.lastMoveEvent=x,this.lastMoveEventInfo=ha(S,this.transformPagePoint),Ce.update(this.updatePoint,!0)},this.handlePointerUp=(x,S)=>{this.end();const{onEnd:A,onSessionEnd:N,resumeAnimation:M}=this.handlers;if(this.dragSnapToOrigin&&M&&M(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const R=ma(x.type==="pointercancel"?this.lastMoveEventInfo:ha(S,this.transformPagePoint),this.history);this.startEvent&&A&&A(x,R),N&&N(x,R)},!su(r))return;this.dragSnapToOrigin=d,this.handlers=o,this.transformPagePoint=l,this.contextWindow=u||window;const f=Ni(r),p=ha(f,this.transformPagePoint),{point:m}=p,{timestamp:g}=Ke;this.history=[{...m,timestamp:g}];const{onSessionStart:y}=o;y&&y(r,ma(p,this.history)),this.removeListeners=Ri(xi(this.contextWindow,"pointermove",this.handlePointerMove),xi(this.contextWindow,"pointerup",this.handlePointerUp),xi(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(r){this.handlers=r}end(){this.removeListeners&&this.removeListeners(),Sn(this.updatePoint)}}function ha(n,r){return r?{point:r(n.point)}:n}function ep(n,r){return{x:n.x-r.x,y:n.y-r.y}}function ma({point:n},r){return{point:n,delta:ep(n,um(r)),offset:ep(n,vx(r)),velocity:xx(r,.1)}}function vx(n){return n[0]}function um(n){return n[n.length-1]}function xx(n,r){if(n.length<2)return{x:0,y:0};let o=n.length-1,l=null;const u=um(n);for(;o>=0&&(l=n[o],!(u.timestamp-l.timestamp>Yt(r)));)o--;if(!l)return{x:0,y:0};const d=Xt(u.timestamp-l.timestamp);if(d===0)return{x:0,y:0};const f={x:(u.x-l.x)/d,y:(u.y-l.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}const cm=1e-4,wx=1-cm,Sx=1+cm,fm=.01,kx=0-fm,Cx=0+fm;function ht(n){return n.max-n.min}function Px(n,r,o){return Math.abs(n-r)<=o}function tp(n,r,o,l=.5){n.origin=l,n.originPoint=Me(r.min,r.max,n.origin),n.scale=ht(o)/ht(r),n.translate=Me(o.min,o.max,n.origin)-n.originPoint,(n.scale>=wx&&n.scale<=Sx||isNaN(n.scale))&&(n.scale=1),(n.translate>=kx&&n.translate<=Cx||isNaN(n.translate))&&(n.translate=0)}function wi(n,r,o,l){tp(n.x,r.x,o.x,l?l.originX:void 0),tp(n.y,r.y,o.y,l?l.originY:void 0)}function np(n,r,o){n.min=o.min+r.min,n.max=n.min+ht(r)}function Tx(n,r,o){np(n.x,r.x,o.x),np(n.y,r.y,o.y)}function rp(n,r,o){n.min=r.min-o.min,n.max=n.min+ht(r)}function Si(n,r,o){rp(n.x,r.x,o.x),rp(n.y,r.y,o.y)}function Ex(n,{min:r,max:o},l){return r!==void 0&&n<r?n=l?Me(r,n,l.min):Math.max(n,r):o!==void 0&&n>o&&(n=l?Me(o,n,l.max):Math.min(n,o)),n}function ip(n,r,o){return{min:r!==void 0?n.min+r:void 0,max:o!==void 0?n.max+o-(n.max-n.min):void 0}}function Ax(n,{top:r,left:o,bottom:l,right:u}){return{x:ip(n.x,o,u),y:ip(n.y,r,l)}}function op(n,r){let o=r.min-n.min,l=r.max-n.max;return r.max-r.min<n.max-n.min&&([o,l]=[l,o]),{min:o,max:l}}function Mx(n,r){return{x:op(n.x,r.x),y:op(n.y,r.y)}}function Rx(n,r){let o=.5;const l=ht(n),u=ht(r);return u>l?o=wr(r.min,r.max-l,n.min):l>u&&(o=wr(n.min,n.max-u,r.min)),Zt(0,1,o)}function Nx(n,r){const o={};return r.min!==void 0&&(o.min=r.min-n.min),r.max!==void 0&&(o.max=r.max-n.min),o}const _a=.35;function Dx(n=_a){return n===!1?n=0:n===!0&&(n=_a),{x:sp(n,"left","right"),y:sp(n,"top","bottom")}}function sp(n,r,o){return{min:lp(n,r),max:lp(n,o)}}function lp(n,r){return typeof n=="number"?n:n[r]||0}const ap=()=>({translate:0,scale:1,origin:0,originPoint:0}),gr=()=>({x:ap(),y:ap()}),up=()=>({min:0,max:0}),Le=()=>({x:up(),y:up()});function Ct(n){return[n("x"),n("y")]}function dm({top:n,left:r,right:o,bottom:l}){return{x:{min:r,max:o},y:{min:n,max:l}}}function Vx({x:n,y:r}){return{top:r.min,right:n.max,bottom:r.max,left:n.min}}function Lx(n,r){if(!r)return n;const o=r({x:n.left,y:n.top}),l=r({x:n.right,y:n.bottom});return{top:o.y,left:o.x,bottom:l.y,right:l.x}}function ga(n){return n===void 0||n===1}function ja({scale:n,scaleX:r,scaleY:o}){return!ga(n)||!ga(r)||!ga(o)}function zn(n){return ja(n)||pm(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function pm(n){return cp(n.x)||cp(n.y)}function cp(n){return n&&n!=="0%"}function ns(n,r,o){const l=n-o,u=r*l;return o+u}function fp(n,r,o,l,u){return u!==void 0&&(n=ns(n,u,l)),ns(n,o,l)+r}function Ia(n,r=0,o=1,l,u){n.min=fp(n.min,r,o,l,u),n.max=fp(n.max,r,o,l,u)}function hm(n,{x:r,y:o}){Ia(n.x,r.translate,r.scale,r.originPoint),Ia(n.y,o.translate,o.scale,o.originPoint)}const dp=.999999999999,pp=1.0000000000001;function _x(n,r,o,l=!1){const u=o.length;if(!u)return;r.x=r.y=1;let d,f;for(let p=0;p<u;p++){d=o[p],f=d.projectionDelta;const{visualElement:m}=d.options;m&&m.props.style&&m.props.style.display==="contents"||(l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&vr(n,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(r.x*=f.x.scale,r.y*=f.y.scale,hm(n,f)),l&&zn(d.latestValues)&&vr(n,d.latestValues))}r.x<pp&&r.x>dp&&(r.x=1),r.y<pp&&r.y>dp&&(r.y=1)}function yr(n,r){n.min=n.min+r,n.max=n.max+r}function hp(n,r,o,l,u=.5){const d=Me(n.min,n.max,u);Ia(n,r,o,d,l)}function vr(n,r){hp(n.x,r.x,r.scaleX,r.scale,r.originX),hp(n.y,r.y,r.scaleY,r.scale,r.originY)}function mm(n,r){return dm(Lx(n.getBoundingClientRect(),r))}function jx(n,r,o){const l=mm(n,o),{scroll:u}=r;return u&&(yr(l.x,u.offset.x),yr(l.y,u.offset.y)),l}const gm=({current:n})=>n?n.ownerDocument.defaultView:null,Ix=new WeakMap;class Ox{constructor(r){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Le(),this.visualElement=r}start(r,{snapToCursor:o=!1}={}){const{presenceContext:l}=this.visualElement;if(l&&l.isPresent===!1)return;const u=y=>{const{dragSnapToOrigin:x}=this.getProps();x?this.pauseAnimation():this.stopAnimation(),o&&this.snapToCursor(Ni(y).point)},d=(y,x)=>{const{drag:S,dragPropagation:A,onDragStart:N}=this.getProps();if(S&&!A&&(this.openDragLock&&this.openDragLock(),this.openDragLock=R0(S),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ct(R=>{let _=this.getAxisMotionValue(R).get()||0;if(zt.test(_)){const{projection:F}=this.visualElement;if(F&&F.layout){const U=F.layout.layoutBox[R];U&&(_=ht(U)*(parseFloat(_)/100))}}this.originPoint[R]=_}),N&&Ce.postRender(()=>N(y,x)),Ea(this.visualElement,"transform");const{animationState:M}=this.visualElement;M&&M.setActive("whileDrag",!0)},f=(y,x)=>{const{dragPropagation:S,dragDirectionLock:A,onDirectionLock:N,onDrag:M}=this.getProps();if(!S&&!this.openDragLock)return;const{offset:R}=x;if(A&&this.currentDirection===null){this.currentDirection=zx(R),this.currentDirection!==null&&N&&N(this.currentDirection);return}this.updateAxis("x",x.point,R),this.updateAxis("y",x.point,R),this.visualElement.render(),M&&M(y,x)},p=(y,x)=>this.stop(y,x),m=()=>Ct(y=>{var x;return this.getAnimationState(y)==="paused"&&((x=this.getAxisMotionValue(y).animation)===null||x===void 0?void 0:x.play())}),{dragSnapToOrigin:g}=this.getProps();this.panSession=new am(r,{onSessionStart:u,onStart:d,onMove:f,onSessionEnd:p,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:g,contextWindow:gm(this.visualElement)})}stop(r,o){const l=this.isDragging;if(this.cancel(),!l)return;const{velocity:u}=o;this.startAnimation(u);const{onDragEnd:d}=this.getProps();d&&Ce.postRender(()=>d(r,o))}cancel(){this.isDragging=!1;const{projection:r,animationState:o}=this.visualElement;r&&(r.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:l}=this.getProps();!l&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),o&&o.setActive("whileDrag",!1)}updateAxis(r,o,l){const{drag:u}=this.getProps();if(!l||!Ho(r,u,this.currentDirection))return;const d=this.getAxisMotionValue(r);let f=this.originPoint[r]+l[r];this.constraints&&this.constraints[r]&&(f=Ex(f,this.constraints[r],this.elastic[r])),d.set(f)}resolveConstraints(){var r;const{dragConstraints:o,dragElastic:l}=this.getProps(),u=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(r=this.visualElement.projection)===null||r===void 0?void 0:r.layout,d=this.constraints;o&&hr(o)?this.constraints||(this.constraints=this.resolveRefConstraints()):o&&u?this.constraints=Ax(u.layoutBox,o):this.constraints=!1,this.elastic=Dx(l),d!==this.constraints&&u&&this.constraints&&!this.hasMutatedConstraints&&Ct(f=>{this.constraints!==!1&&this.getAxisMotionValue(f)&&(this.constraints[f]=Nx(u.layoutBox[f],this.constraints[f]))})}resolveRefConstraints(){const{dragConstraints:r,onMeasureDragConstraints:o}=this.getProps();if(!r||!hr(r))return!1;const l=r.current,{projection:u}=this.visualElement;if(!u||!u.layout)return!1;const d=jx(l,u.root,this.visualElement.getTransformPagePoint());let f=Mx(u.layout.layoutBox,d);if(o){const p=o(Vx(f));this.hasMutatedConstraints=!!p,p&&(f=dm(p))}return f}startAnimation(r){const{drag:o,dragMomentum:l,dragElastic:u,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:p}=this.getProps(),m=this.constraints||{},g=Ct(y=>{if(!Ho(y,o,this.currentDirection))return;let x=m&&m[y]||{};f&&(x={min:0,max:0});const S=u?200:1e6,A=u?40:1e7,N={type:"inertia",velocity:l?r[y]:0,bounceStiffness:S,bounceDamping:A,timeConstant:750,restDelta:1,restSpeed:10,...d,...x};return this.startAxisValueAnimation(y,N)});return Promise.all(g).then(p)}startAxisValueAnimation(r,o){const l=this.getAxisMotionValue(r);return Ea(this.visualElement,r),l.start(vu(r,l,0,o,this.visualElement,!1))}stopAnimation(){Ct(r=>this.getAxisMotionValue(r).stop())}pauseAnimation(){Ct(r=>{var o;return(o=this.getAxisMotionValue(r).animation)===null||o===void 0?void 0:o.pause()})}getAnimationState(r){var o;return(o=this.getAxisMotionValue(r).animation)===null||o===void 0?void 0:o.state}getAxisMotionValue(r){const o=`_drag${r.toUpperCase()}`,l=this.visualElement.getProps(),u=l[o];return u||this.visualElement.getValue(r,(l.initial?l.initial[r]:void 0)||0)}snapToCursor(r){Ct(o=>{const{drag:l}=this.getProps();if(!Ho(o,l,this.currentDirection))return;const{projection:u}=this.visualElement,d=this.getAxisMotionValue(o);if(u&&u.layout){const{min:f,max:p}=u.layout.layoutBox[o];d.set(r[o]-Me(f,p,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:r,dragConstraints:o}=this.getProps(),{projection:l}=this.visualElement;if(!hr(o)||!l||!this.constraints)return;this.stopAnimation();const u={x:0,y:0};Ct(f=>{const p=this.getAxisMotionValue(f);if(p&&this.constraints!==!1){const m=p.get();u[f]=Rx({min:m,max:m},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",l.root&&l.root.updateScroll(),l.updateLayout(),this.resolveConstraints(),Ct(f=>{if(!Ho(f,r,null))return;const p=this.getAxisMotionValue(f),{min:m,max:g}=this.constraints[f];p.set(Me(m,g,u[f]))})}addListeners(){if(!this.visualElement.current)return;Ix.set(this.visualElement,this);const r=this.visualElement.current,o=xi(r,"pointerdown",m=>{const{drag:g,dragListener:y=!0}=this.getProps();g&&y&&this.start(m)}),l=()=>{const{dragConstraints:m}=this.getProps();hr(m)&&m.current&&(this.constraints=this.resolveRefConstraints())},{projection:u}=this.visualElement,d=u.addEventListener("measure",l);u&&!u.layout&&(u.root&&u.root.updateScroll(),u.updateLayout()),Ce.read(l);const f=Ei(window,"resize",()=>this.scalePositionWithinConstraints()),p=u.addEventListener("didUpdate",({delta:m,hasLayoutChanged:g})=>{this.isDragging&&g&&(Ct(y=>{const x=this.getAxisMotionValue(y);x&&(this.originPoint[y]+=m[y].translate,x.set(x.get()+m[y].translate))}),this.visualElement.render())});return()=>{f(),o(),d(),p&&p()}}getProps(){const r=this.visualElement.getProps(),{drag:o=!1,dragDirectionLock:l=!1,dragPropagation:u=!1,dragConstraints:d=!1,dragElastic:f=_a,dragMomentum:p=!0}=r;return{...r,drag:o,dragDirectionLock:l,dragPropagation:u,dragConstraints:d,dragElastic:f,dragMomentum:p}}}function Ho(n,r,o){return(r===!0||r===n)&&(o===null||o===n)}function zx(n,r=10){let o=null;return Math.abs(n.y)>r?o="y":Math.abs(n.x)>r&&(o="x"),o}class Fx extends Cn{constructor(r){super(r),this.removeGroupControls=pt,this.removeListeners=pt,this.controls=new Ox(r)}mount(){const{dragControls:r}=this.node.getProps();r&&(this.removeGroupControls=r.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||pt}unmount(){this.removeGroupControls(),this.removeListeners()}}const mp=n=>(r,o)=>{n&&Ce.postRender(()=>n(r,o))};class bx extends Cn{constructor(){super(...arguments),this.removePointerDownListener=pt}onPointerDown(r){this.session=new am(r,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:gm(this.node)})}createPanHandlers(){const{onPanSessionStart:r,onPanStart:o,onPan:l,onPanEnd:u}=this.node.getProps();return{onSessionStart:mp(r),onStart:mp(o),onMove:l,onEnd:(d,f)=>{delete this.session,u&&Ce.postRender(()=>u(d,f))}}}mount(){this.removePointerDownListener=xi(this.node.current,"pointerdown",r=>this.onPointerDown(r))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Qo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function gp(n,r){return r.max===r.min?0:n/(r.max-r.min)*100}const hi={correct:(n,r)=>{if(!r.target)return n;if(typeof n=="string")if(te.test(n))n=parseFloat(n);else return n;const o=gp(n,r.target.x),l=gp(n,r.target.y);return`${o}% ${l}%`}},Bx={correct:(n,{treeScale:r,projectionDelta:o})=>{const l=n,u=kn.parse(n);if(u.length>5)return l;const d=kn.createTransformer(n),f=typeof u[0]!="number"?1:0,p=o.x.scale*r.x,m=o.y.scale*r.y;u[0+f]/=p,u[1+f]/=m;const g=Me(p,m,.5);return typeof u[2+f]=="number"&&(u[2+f]/=g),typeof u[3+f]=="number"&&(u[3+f]/=g),d(u)}};class Ux extends b.Component{componentDidMount(){const{visualElement:r,layoutGroup:o,switchLayoutGroup:l,layoutId:u}=this.props,{projection:d}=r;a0(Wx),d&&(o.group&&o.group.add(d),l&&l.register&&u&&l.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),Qo.hasEverUpdated=!0}getSnapshotBeforeUpdate(r){const{layoutDependency:o,visualElement:l,drag:u,isPresent:d}=this.props,f=l.projection;return f&&(f.isPresent=d,u||r.layoutDependency!==o||o===void 0?f.willUpdate():this.safeToRemove(),r.isPresent!==d&&(d?f.promote():f.relegate()||Ce.postRender(()=>{const p=f.getStack();(!p||!p.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:r}=this.props.visualElement;r&&(r.root.didUpdate(),Ga.postRender(()=>{!r.currentAnimation&&r.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:r,layoutGroup:o,switchLayoutGroup:l}=this.props,{projection:u}=r;u&&(u.scheduleCheckAfterUnmount(),o&&o.group&&o.group.remove(u),l&&l.deregister&&l.deregister(u))}safeToRemove(){const{safeToRemove:r}=this.props;r&&r()}render(){return null}}function ym(n){const[r,o]=Cv(),l=b.useContext(Jp);return I.jsx(Ux,{...n,layoutGroup:l,switchLayoutGroup:b.useContext(lh),isPresent:r,safeToRemove:o})}const Wx={borderRadius:{...hi,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:hi,borderTopRightRadius:hi,borderBottomLeftRadius:hi,borderBottomRightRadius:hi,boxShadow:Bx};function $x(n,r,o){const l=qe(n)?n:Pi(n);return l.start(vu("",l,r,o)),l.animation}function Hx(n){return n instanceof SVGElement&&n.tagName!=="svg"}const Kx=(n,r)=>n.depth-r.depth;class Gx{constructor(){this.children=[],this.isDirty=!1}add(r){lu(this.children,r),this.isDirty=!0}remove(r){au(this.children,r),this.isDirty=!0}forEach(r){this.isDirty&&this.children.sort(Kx),this.isDirty=!1,this.children.forEach(r)}}function Qx(n,r){const o=Ft.now(),l=({timestamp:u})=>{const d=u-o;d>=r&&(Sn(l),n(d-r))};return Ce.read(l,!0),()=>Sn(l)}const vm=["TopLeft","TopRight","BottomLeft","BottomRight"],Yx=vm.length,yp=n=>typeof n=="string"?parseFloat(n):n,vp=n=>typeof n=="number"||te.test(n);function Xx(n,r,o,l,u,d){u?(n.opacity=Me(0,o.opacity!==void 0?o.opacity:1,Zx(l)),n.opacityExit=Me(r.opacity!==void 0?r.opacity:1,0,qx(l))):d&&(n.opacity=Me(r.opacity!==void 0?r.opacity:1,o.opacity!==void 0?o.opacity:1,l));for(let f=0;f<Yx;f++){const p=`border${vm[f]}Radius`;let m=xp(r,p),g=xp(o,p);if(m===void 0&&g===void 0)continue;m||(m=0),g||(g=0),m===0||g===0||vp(m)===vp(g)?(n[p]=Math.max(Me(yp(m),yp(g),l),0),(zt.test(g)||zt.test(m))&&(n[p]+="%")):n[p]=g}(r.rotate||o.rotate)&&(n.rotate=Me(r.rotate||0,o.rotate||0,l))}function xp(n,r){return n[r]!==void 0?n[r]:n.borderRadius}const Zx=xm(0,.5,Oh),qx=xm(.5,.95,pt);function xm(n,r,o){return l=>l<n?0:l>r?1:o(wr(n,r,l))}function wp(n,r){n.min=r.min,n.max=r.max}function kt(n,r){wp(n.x,r.x),wp(n.y,r.y)}function Sp(n,r){n.translate=r.translate,n.scale=r.scale,n.originPoint=r.originPoint,n.origin=r.origin}function kp(n,r,o,l,u){return n-=r,n=ns(n,1/o,l),u!==void 0&&(n=ns(n,1/u,l)),n}function Jx(n,r=0,o=1,l=.5,u,d=n,f=n){if(zt.test(r)&&(r=parseFloat(r),r=Me(f.min,f.max,r/100)-f.min),typeof r!="number")return;let p=Me(d.min,d.max,l);n===d&&(p-=r),n.min=kp(n.min,r,o,p,u),n.max=kp(n.max,r,o,p,u)}function Cp(n,r,[o,l,u],d,f){Jx(n,r[o],r[l],r[u],r.scale,d,f)}const ew=["x","scaleX","originX"],tw=["y","scaleY","originY"];function Pp(n,r,o,l){Cp(n.x,r,ew,o?o.x:void 0,l?l.x:void 0),Cp(n.y,r,tw,o?o.y:void 0,l?l.y:void 0)}function Tp(n){return n.translate===0&&n.scale===1}function wm(n){return Tp(n.x)&&Tp(n.y)}function Ep(n,r){return n.min===r.min&&n.max===r.max}function nw(n,r){return Ep(n.x,r.x)&&Ep(n.y,r.y)}function Ap(n,r){return Math.round(n.min)===Math.round(r.min)&&Math.round(n.max)===Math.round(r.max)}function Sm(n,r){return Ap(n.x,r.x)&&Ap(n.y,r.y)}function Mp(n){return ht(n.x)/ht(n.y)}function Rp(n,r){return n.translate===r.translate&&n.scale===r.scale&&n.originPoint===r.originPoint}class rw{constructor(){this.members=[]}add(r){lu(this.members,r),r.scheduleRender()}remove(r){if(au(this.members,r),r===this.prevLead&&(this.prevLead=void 0),r===this.lead){const o=this.members[this.members.length-1];o&&this.promote(o)}}relegate(r){const o=this.members.findIndex(u=>r===u);if(o===0)return!1;let l;for(let u=o;u>=0;u--){const d=this.members[u];if(d.isPresent!==!1){l=d;break}}return l?(this.promote(l),!0):!1}promote(r,o){const l=this.lead;if(r!==l&&(this.prevLead=l,this.lead=r,r.show(),l)){l.instance&&l.scheduleRender(),r.scheduleRender(),r.resumeFrom=l,o&&(r.resumeFrom.preserveOpacity=!0),l.snapshot&&(r.snapshot=l.snapshot,r.snapshot.latestValues=l.animationValues||l.latestValues),r.root&&r.root.isUpdating&&(r.isLayoutDirty=!0);const{crossfade:u}=r.options;u===!1&&l.hide()}}exitAnimationComplete(){this.members.forEach(r=>{const{options:o,resumingFrom:l}=r;o.onExitComplete&&o.onExitComplete(),l&&l.options.onExitComplete&&l.options.onExitComplete()})}scheduleRender(){this.members.forEach(r=>{r.instance&&r.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function iw(n,r,o){let l="";const u=n.x.translate/r.x,d=n.y.translate/r.y,f=(o==null?void 0:o.z)||0;if((u||d||f)&&(l=`translate3d(${u}px, ${d}px, ${f}px) `),(r.x!==1||r.y!==1)&&(l+=`scale(${1/r.x}, ${1/r.y}) `),o){const{transformPerspective:g,rotate:y,rotateX:x,rotateY:S,skewX:A,skewY:N}=o;g&&(l=`perspective(${g}px) ${l}`),y&&(l+=`rotate(${y}deg) `),x&&(l+=`rotateX(${x}deg) `),S&&(l+=`rotateY(${S}deg) `),A&&(l+=`skewX(${A}deg) `),N&&(l+=`skewY(${N}deg) `)}const p=n.x.scale*r.x,m=n.y.scale*r.y;return(p!==1||m!==1)&&(l+=`scale(${p}, ${m})`),l||"none"}const Fn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},yi=typeof window<"u"&&window.MotionDebug!==void 0,ya=["","X","Y","Z"],ow={visibility:"hidden"},Np=1e3;let sw=0;function va(n,r,o,l){const{latestValues:u}=r;u[n]&&(o[n]=u[n],r.setStaticValue(n,0),l&&(l[n]=0))}function km(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:r}=n.options;if(!r)return;const o=Nh(r);if(window.MotionHasOptimisedAnimation(o,"transform")){const{layout:u,layoutId:d}=n.options;window.MotionCancelOptimisedAnimation(o,"transform",Ce,!(u||d))}const{parent:l}=n;l&&!l.hasCheckedOptimisedAppear&&km(l)}function Cm({attachResizeListener:n,defaultParent:r,measureScroll:o,checkIsScrollRoot:l,resetTransform:u}){return class{constructor(f={},p=r==null?void 0:r()){this.id=sw++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,yi&&(Fn.totalNodes=Fn.resolvedTargetDeltas=Fn.recalculatedProjection=0),this.nodes.forEach(uw),this.nodes.forEach(hw),this.nodes.forEach(mw),this.nodes.forEach(cw),yi&&window.MotionDebug.record(Fn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=p?p.root||p:this,this.path=p?[...p.path,p]:[],this.parent=p,this.depth=p?p.depth+1:0;for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new Gx)}addEventListener(f,p){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new uu),this.eventHandlers.get(f).add(p)}notifyListeners(f,...p){const m=this.eventHandlers.get(f);m&&m.notify(...p)}hasListeners(f){return this.eventHandlers.has(f)}mount(f,p=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Hx(f),this.instance=f;const{layoutId:m,layout:g,visualElement:y}=this.options;if(y&&!y.current&&y.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),p&&(g||m)&&(this.isLayoutDirty=!0),n){let x;const S=()=>this.root.updateBlockedByResize=!1;n(f,()=>{this.root.updateBlockedByResize=!0,x&&x(),x=Qx(S,250),Qo.hasAnimatedSinceResize&&(Qo.hasAnimatedSinceResize=!1,this.nodes.forEach(Vp))})}m&&this.root.registerSharedNode(m,this),this.options.animate!==!1&&y&&(m||g)&&this.addEventListener("didUpdate",({delta:x,hasLayoutChanged:S,hasRelativeTargetChanged:A,layout:N})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const M=this.options.transition||y.getDefaultTransition()||ww,{onLayoutAnimationStart:R,onLayoutAnimationComplete:_}=y.getProps(),F=!this.targetLayout||!Sm(this.targetLayout,N)||A,U=!S&&A;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||U||S&&(F||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(x,U);const q={...ru(M,"layout"),onPlay:R,onComplete:_};(y.shouldReduceMotion||this.options.layoutRoot)&&(q.delay=0,q.type=!1),this.startAnimation(q)}else S||Vp(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=N})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Sn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(gw),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&km(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let y=0;y<this.path.length;y++){const x=this.path[y];x.shouldResetTransform=!0,x.updateScroll("snapshot"),x.options.layoutRoot&&x.willUpdate(!1)}const{layoutId:p,layout:m}=this.options;if(p===void 0&&!m)return;const g=this.getTransformTemplate();this.prevTransformTemplateValue=g?g(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Dp);return}this.isUpdating||this.nodes.forEach(dw),this.isUpdating=!1,this.nodes.forEach(pw),this.nodes.forEach(lw),this.nodes.forEach(aw),this.clearAllSnapshots();const p=Ft.now();Ke.delta=Zt(0,1e3/60,p-Ke.timestamp),Ke.timestamp=p,Ke.isProcessing=!0,aa.update.process(Ke),aa.preRender.process(Ke),aa.render.process(Ke),Ke.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ga.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(fw),this.sharedNodes.forEach(yw)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ce.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ce.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let m=0;m<this.path.length;m++)this.path[m].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Le(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:p}=this.options;p&&p.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let p=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(p=!1),p){const m=l(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:m,offset:o(this.instance),wasRoot:this.scroll?this.scroll.isRoot:m}}}resetTransform(){if(!u)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,p=this.projectionDelta&&!wm(this.projectionDelta),m=this.getTransformTemplate(),g=m?m(this.latestValues,""):void 0,y=g!==this.prevTransformTemplateValue;f&&(p||zn(this.latestValues)||y)&&(u(this.instance,g),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const p=this.measurePageBox();let m=this.removeElementScroll(p);return f&&(m=this.removeTransform(m)),Sw(m),{animationId:this.root.animationId,measuredBox:p,layoutBox:m,latestValues:{},source:this.id}}measurePageBox(){var f;const{visualElement:p}=this.options;if(!p)return Le();const m=p.measureViewportBox();if(!(((f=this.scroll)===null||f===void 0?void 0:f.wasRoot)||this.path.some(kw))){const{scroll:y}=this.root;y&&(yr(m.x,y.offset.x),yr(m.y,y.offset.y))}return m}removeElementScroll(f){var p;const m=Le();if(kt(m,f),!((p=this.scroll)===null||p===void 0)&&p.wasRoot)return m;for(let g=0;g<this.path.length;g++){const y=this.path[g],{scroll:x,options:S}=y;y!==this.root&&x&&S.layoutScroll&&(x.wasRoot&&kt(m,f),yr(m.x,x.offset.x),yr(m.y,x.offset.y))}return m}applyTransform(f,p=!1){const m=Le();kt(m,f);for(let g=0;g<this.path.length;g++){const y=this.path[g];!p&&y.options.layoutScroll&&y.scroll&&y!==y.root&&vr(m,{x:-y.scroll.offset.x,y:-y.scroll.offset.y}),zn(y.latestValues)&&vr(m,y.latestValues)}return zn(this.latestValues)&&vr(m,this.latestValues),m}removeTransform(f){const p=Le();kt(p,f);for(let m=0;m<this.path.length;m++){const g=this.path[m];if(!g.instance||!zn(g.latestValues))continue;ja(g.latestValues)&&g.updateSnapshot();const y=Le(),x=g.measurePageBox();kt(y,x),Pp(p,g.latestValues,g.snapshot?g.snapshot.layoutBox:void 0,y)}return zn(this.latestValues)&&Pp(p,this.latestValues),p}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options={...this.options,...f,crossfade:f.crossfade!==void 0?f.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ke.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){var p;const m=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=m.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=m.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=m.isSharedProjectionDirty);const g=!!this.resumingFrom||this!==m;if(!(f||g&&this.isSharedProjectionDirty||this.isProjectionDirty||!((p=this.parent)===null||p===void 0)&&p.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:x,layoutId:S}=this.options;if(!(!this.layout||!(x||S))){if(this.resolvedRelativeTargetAt=Ke.timestamp,!this.targetDelta&&!this.relativeTarget){const A=this.getClosestProjectingParent();A&&A.layout&&this.animationProgress!==1?(this.relativeParent=A,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Le(),this.relativeTargetOrigin=Le(),Si(this.relativeTargetOrigin,this.layout.layoutBox,A.layout.layoutBox),kt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=Le(),this.targetWithTransforms=Le()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Tx(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):kt(this.target,this.layout.layoutBox),hm(this.target,this.targetDelta)):kt(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const A=this.getClosestProjectingParent();A&&!!A.resumingFrom==!!this.resumingFrom&&!A.options.layoutScroll&&A.target&&this.animationProgress!==1?(this.relativeParent=A,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Le(),this.relativeTargetOrigin=Le(),Si(this.relativeTargetOrigin,this.target,A.target),kt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}yi&&Fn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ja(this.parent.latestValues)||pm(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var f;const p=this.getLead(),m=!!this.resumingFrom||this!==p;let g=!0;if((this.isProjectionDirty||!((f=this.parent)===null||f===void 0)&&f.isProjectionDirty)&&(g=!1),m&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(g=!1),this.resolvedRelativeTargetAt===Ke.timestamp&&(g=!1),g)return;const{layout:y,layoutId:x}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(y||x))return;kt(this.layoutCorrected,this.layout.layoutBox);const S=this.treeScale.x,A=this.treeScale.y;_x(this.layoutCorrected,this.treeScale,this.path,m),p.layout&&!p.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(p.target=p.layout.layoutBox,p.targetWithTransforms=Le());const{target:N}=p;if(!N){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Sp(this.prevProjectionDelta.x,this.projectionDelta.x),Sp(this.prevProjectionDelta.y,this.projectionDelta.y)),wi(this.projectionDelta,this.layoutCorrected,N,this.latestValues),(this.treeScale.x!==S||this.treeScale.y!==A||!Rp(this.projectionDelta.x,this.prevProjectionDelta.x)||!Rp(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",N)),yi&&Fn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){var p;if((p=this.options.visualElement)===null||p===void 0||p.scheduleRender(),f){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=gr(),this.projectionDelta=gr(),this.projectionDeltaWithTransform=gr()}setAnimationOrigin(f,p=!1){const m=this.snapshot,g=m?m.latestValues:{},y={...this.latestValues},x=gr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!p;const S=Le(),A=m?m.source:void 0,N=this.layout?this.layout.source:void 0,M=A!==N,R=this.getStack(),_=!R||R.members.length<=1,F=!!(M&&!_&&this.options.crossfade===!0&&!this.path.some(xw));this.animationProgress=0;let U;this.mixTargetDelta=q=>{const G=q/1e3;Lp(x.x,f.x,G),Lp(x.y,f.y,G),this.setTargetDelta(x),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Si(S,this.layout.layoutBox,this.relativeParent.layout.layoutBox),vw(this.relativeTarget,this.relativeTargetOrigin,S,G),U&&nw(this.relativeTarget,U)&&(this.isProjectionDirty=!1),U||(U=Le()),kt(U,this.relativeTarget)),M&&(this.animationValues=y,Xx(y,g,this.latestValues,G,F,_)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=G},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Sn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ce.update(()=>{Qo.hasAnimatedSinceResize=!0,this.currentAnimation=$x(0,Np,{...f,onUpdate:p=>{this.mixTargetDelta(p),f.onUpdate&&f.onUpdate(p)},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Np),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:p,target:m,layout:g,latestValues:y}=f;if(!(!p||!m||!g)){if(this!==f&&this.layout&&g&&Pm(this.options.animationType,this.layout.layoutBox,g.layoutBox)){m=this.target||Le();const x=ht(this.layout.layoutBox.x);m.x.min=f.target.x.min,m.x.max=m.x.min+x;const S=ht(this.layout.layoutBox.y);m.y.min=f.target.y.min,m.y.max=m.y.min+S}kt(p,m),vr(p,y),wi(this.projectionDeltaWithTransform,this.layoutCorrected,p,y)}}registerSharedNode(f,p){this.sharedNodes.has(f)||this.sharedNodes.set(f,new rw),this.sharedNodes.get(f).add(p);const g=p.options.initialPromotionConfig;p.promote({transition:g?g.transition:void 0,preserveFollowOpacity:g&&g.shouldPreserveFollowOpacity?g.shouldPreserveFollowOpacity(p):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){var f;const{layoutId:p}=this.options;return p?((f=this.getStack())===null||f===void 0?void 0:f.lead)||this:this}getPrevLead(){var f;const{layoutId:p}=this.options;return p?(f=this.getStack())===null||f===void 0?void 0:f.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:p,preserveFollowOpacity:m}={}){const g=this.getStack();g&&g.promote(this,m),f&&(this.projectionDelta=void 0,this.needsReset=!0),p&&this.setOptions({transition:p})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let p=!1;const{latestValues:m}=f;if((m.z||m.rotate||m.rotateX||m.rotateY||m.rotateZ||m.skewX||m.skewY)&&(p=!0),!p)return;const g={};m.z&&va("z",f,g,this.animationValues);for(let y=0;y<ya.length;y++)va(`rotate${ya[y]}`,f,g,this.animationValues),va(`skew${ya[y]}`,f,g,this.animationValues);f.render();for(const y in g)f.setStaticValue(y,g[y]),this.animationValues&&(this.animationValues[y]=g[y]);f.scheduleRender()}getProjectionStyles(f){var p,m;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ow;const g={visibility:""},y=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,g.opacity="",g.pointerEvents=Ko(f==null?void 0:f.pointerEvents)||"",g.transform=y?y(this.latestValues,""):"none",g;const x=this.getLead();if(!this.projectionDelta||!this.layout||!x.target){const M={};return this.options.layoutId&&(M.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,M.pointerEvents=Ko(f==null?void 0:f.pointerEvents)||""),this.hasProjected&&!zn(this.latestValues)&&(M.transform=y?y({},""):"none",this.hasProjected=!1),M}const S=x.animationValues||x.latestValues;this.applyTransformsToTarget(),g.transform=iw(this.projectionDeltaWithTransform,this.treeScale,S),y&&(g.transform=y(S,g.transform));const{x:A,y:N}=this.projectionDelta;g.transformOrigin=`${A.origin*100}% ${N.origin*100}% 0`,x.animationValues?g.opacity=x===this?(m=(p=S.opacity)!==null&&p!==void 0?p:this.latestValues.opacity)!==null&&m!==void 0?m:1:this.preserveOpacity?this.latestValues.opacity:S.opacityExit:g.opacity=x===this?S.opacity!==void 0?S.opacity:"":S.opacityExit!==void 0?S.opacityExit:0;for(const M in Zo){if(S[M]===void 0)continue;const{correct:R,applyTo:_}=Zo[M],F=g.transform==="none"?S[M]:R(S[M],x);if(_){const U=_.length;for(let q=0;q<U;q++)g[_[q]]=F}else g[M]=F}return this.options.layoutId&&(g.pointerEvents=x===this?Ko(f==null?void 0:f.pointerEvents)||"":"none"),g}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>{var p;return(p=f.currentAnimation)===null||p===void 0?void 0:p.stop()}),this.root.nodes.forEach(Dp),this.root.sharedNodes.clear()}}}function lw(n){n.updateLayout()}function aw(n){var r;const o=((r=n.resumeFrom)===null||r===void 0?void 0:r.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&o&&n.hasListeners("didUpdate")){const{layoutBox:l,measuredBox:u}=n.layout,{animationType:d}=n.options,f=o.source!==n.layout.source;d==="size"?Ct(x=>{const S=f?o.measuredBox[x]:o.layoutBox[x],A=ht(S);S.min=l[x].min,S.max=S.min+A}):Pm(d,o.layoutBox,l)&&Ct(x=>{const S=f?o.measuredBox[x]:o.layoutBox[x],A=ht(l[x]);S.max=S.min+A,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[x].max=n.relativeTarget[x].min+A)});const p=gr();wi(p,l,o.layoutBox);const m=gr();f?wi(m,n.applyTransform(u,!0),o.measuredBox):wi(m,l,o.layoutBox);const g=!wm(p);let y=!1;if(!n.resumeFrom){const x=n.getClosestProjectingParent();if(x&&!x.resumeFrom){const{snapshot:S,layout:A}=x;if(S&&A){const N=Le();Si(N,o.layoutBox,S.layoutBox);const M=Le();Si(M,l,A.layoutBox),Sm(N,M)||(y=!0),x.options.layoutRoot&&(n.relativeTarget=M,n.relativeTargetOrigin=N,n.relativeParent=x)}}}n.notifyListeners("didUpdate",{layout:l,snapshot:o,delta:m,layoutDelta:p,hasLayoutChanged:g,hasRelativeTargetChanged:y})}else if(n.isLead()){const{onExitComplete:l}=n.options;l&&l()}n.options.transition=void 0}function uw(n){yi&&Fn.totalNodes++,n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function cw(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function fw(n){n.clearSnapshot()}function Dp(n){n.clearMeasurements()}function dw(n){n.isLayoutDirty=!1}function pw(n){const{visualElement:r}=n.options;r&&r.getProps().onBeforeLayoutMeasure&&r.notify("BeforeLayoutMeasure"),n.resetTransform()}function Vp(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function hw(n){n.resolveTargetDelta()}function mw(n){n.calcProjection()}function gw(n){n.resetSkewAndRotation()}function yw(n){n.removeLeadSnapshot()}function Lp(n,r,o){n.translate=Me(r.translate,0,o),n.scale=Me(r.scale,1,o),n.origin=r.origin,n.originPoint=r.originPoint}function _p(n,r,o,l){n.min=Me(r.min,o.min,l),n.max=Me(r.max,o.max,l)}function vw(n,r,o,l){_p(n.x,r.x,o.x,l),_p(n.y,r.y,o.y,l)}function xw(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const ww={duration:.45,ease:[.4,0,.1,1]},jp=n=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),Ip=jp("applewebkit/")&&!jp("chrome/")?Math.round:pt;function Op(n){n.min=Ip(n.min),n.max=Ip(n.max)}function Sw(n){Op(n.x),Op(n.y)}function Pm(n,r,o){return n==="position"||n==="preserve-aspect"&&!Px(Mp(r),Mp(o),.2)}function kw(n){var r;return n!==n.root&&((r=n.scroll)===null||r===void 0?void 0:r.wasRoot)}const Cw=Cm({attachResizeListener:(n,r)=>Ei(n,"resize",r),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),xa={current:void 0},Tm=Cm({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!xa.current){const n=new Cw({});n.mount(window),n.setOptions({layoutScroll:!0}),xa.current=n}return xa.current},resetTransform:(n,r)=>{n.style.transform=r!==void 0?r:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),Pw={pan:{Feature:bx},drag:{Feature:Fx,ProjectionNode:Tm,MeasureLayout:ym}};function zp(n,r,o){const{props:l}=n;n.animationState&&l.whileHover&&n.animationState.setActive("whileHover",o==="Start");const u="onHover"+o,d=l[u];d&&Ce.postRender(()=>d(r,Ni(r)))}class Tw extends Cn{mount(){const{current:r}=this.node;r&&(this.unmount=P0(r,o=>(zp(this.node,o,"Start"),l=>zp(this.node,l,"End"))))}unmount(){}}class Ew extends Cn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let r=!1;try{r=this.node.current.matches(":focus-visible")}catch{r=!0}!r||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ri(Ei(this.node.current,"focus",()=>this.onFocus()),Ei(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Fp(n,r,o){const{props:l}=n;n.animationState&&l.whileTap&&n.animationState.setActive("whileTap",o==="Start");const u="onTap"+(o==="End"?"":o),d=l[u];d&&Ce.postRender(()=>d(r,Ni(r)))}class Aw extends Cn{mount(){const{current:r}=this.node;r&&(this.unmount=M0(r,o=>(Fp(this.node,o,"Start"),(l,{success:u})=>Fp(this.node,l,u?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Oa=new WeakMap,wa=new WeakMap,Mw=n=>{const r=Oa.get(n.target);r&&r(n)},Rw=n=>{n.forEach(Mw)};function Nw({root:n,...r}){const o=n||document;wa.has(o)||wa.set(o,{});const l=wa.get(o),u=JSON.stringify(r);return l[u]||(l[u]=new IntersectionObserver(Rw,{root:n,...r})),l[u]}function Dw(n,r,o){const l=Nw(r);return Oa.set(n,o),l.observe(n),()=>{Oa.delete(n),l.unobserve(n)}}const Vw={some:0,all:1};class Lw extends Cn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:r={}}=this.node.getProps(),{root:o,margin:l,amount:u="some",once:d}=r,f={root:o?o.current:void 0,rootMargin:l,threshold:typeof u=="number"?u:Vw[u]},p=m=>{const{isIntersecting:g}=m;if(this.isInView===g||(this.isInView=g,d&&!g&&this.hasEnteredView))return;g&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",g);const{onViewportEnter:y,onViewportLeave:x}=this.node.getProps(),S=g?y:x;S&&S(m)};return Dw(this.node.current,f,p)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:r,prevProps:o}=this.node;["amount","margin","root"].some(_w(r,o))&&this.startObserver()}unmount(){}}function _w({viewport:n={}},{viewport:r={}}={}){return o=>n[o]!==r[o]}const jw={inView:{Feature:Lw},tap:{Feature:Aw},focus:{Feature:Ew},hover:{Feature:Tw}},Iw={layout:{ProjectionNode:Tm,MeasureLayout:ym}},za={current:null},Em={current:!1};function Ow(){if(Em.current=!0,!!Ua)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),r=()=>za.current=n.matches;n.addListener(r),r()}else za.current=!1}const zw=[...Zh,Ze,kn],Fw=n=>zw.find(Xh(n)),bp=new WeakMap;function bw(n,r,o){for(const l in r){const u=r[l],d=o[l];if(qe(u))n.addValue(l,u);else if(qe(d))n.addValue(l,Pi(u,{owner:n}));else if(d!==u)if(n.hasValue(l)){const f=n.getValue(l);f.liveStyle===!0?f.jump(u):f.hasAnimated||f.set(u)}else{const f=n.getStaticValue(l);n.addValue(l,Pi(f!==void 0?f:u,{owner:n}))}}for(const l in o)r[l]===void 0&&n.removeValue(l);return r}const Bp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Bw{scrapeMotionValuesFromProps(r,o,l){return{}}constructor({parent:r,props:o,presenceContext:l,reducedMotionConfig:u,blockInitialAnimation:d,visualState:f},p={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=mu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const A=Ft.now();this.renderScheduledAt<A&&(this.renderScheduledAt=A,Ce.render(this.render,!1,!0))};const{latestValues:m,renderState:g,onUpdate:y}=f;this.onUpdate=y,this.latestValues=m,this.baseTarget={...m},this.initialValues=o.initial?{...m}:{},this.renderState=g,this.parent=r,this.props=o,this.presenceContext=l,this.depth=r?r.depth+1:0,this.reducedMotionConfig=u,this.options=p,this.blockInitialAnimation=!!d,this.isControllingVariants=ss(o),this.isVariantNode=oh(o),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(r&&r.current);const{willChange:x,...S}=this.scrapeMotionValuesFromProps(o,{},this);for(const A in S){const N=S[A];m[A]!==void 0&&qe(N)&&N.set(m[A],!1)}}mount(r){this.current=r,bp.set(r,this),this.projection&&!this.projection.instance&&this.projection.mount(r),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((o,l)=>this.bindToMotionValue(l,o)),Em.current||Ow(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:za.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){bp.delete(this.current),this.projection&&this.projection.unmount(),Sn(this.notifyUpdate),Sn(this.render),this.valueSubscriptions.forEach(r=>r()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const r in this.events)this.events[r].clear();for(const r in this.features){const o=this.features[r];o&&(o.unmount(),o.isMounted=!1)}this.current=null}bindToMotionValue(r,o){this.valueSubscriptions.has(r)&&this.valueSubscriptions.get(r)();const l=Un.has(r),u=o.on("change",p=>{this.latestValues[r]=p,this.props.onUpdate&&Ce.preRender(this.notifyUpdate),l&&this.projection&&(this.projection.isTransformDirty=!0)}),d=o.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,r,o)),this.valueSubscriptions.set(r,()=>{u(),d(),f&&f(),o.owner&&o.stop()})}sortNodePosition(r){return!this.current||!this.sortInstanceNodePosition||this.type!==r.type?0:this.sortInstanceNodePosition(this.current,r.current)}updateFeatures(){let r="animation";for(r in Sr){const o=Sr[r];if(!o)continue;const{isEnabled:l,Feature:u}=o;if(!this.features[r]&&u&&l(this.props)&&(this.features[r]=new u(this)),this.features[r]){const d=this.features[r];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Le()}getStaticValue(r){return this.latestValues[r]}setStaticValue(r,o){this.latestValues[r]=o}update(r,o){(r.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=r,this.prevPresenceContext=this.presenceContext,this.presenceContext=o;for(let l=0;l<Bp.length;l++){const u=Bp[l];this.propEventSubscriptions[u]&&(this.propEventSubscriptions[u](),delete this.propEventSubscriptions[u]);const d="on"+u,f=r[d];f&&(this.propEventSubscriptions[u]=this.on(u,f))}this.prevMotionValues=bw(this,this.scrapeMotionValuesFromProps(r,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(r){return this.props.variants?this.props.variants[r]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(r){const o=this.getClosestVariantNode();if(o)return o.variantChildren&&o.variantChildren.add(r),()=>o.variantChildren.delete(r)}addValue(r,o){const l=this.values.get(r);o!==l&&(l&&this.removeValue(r),this.bindToMotionValue(r,o),this.values.set(r,o),this.latestValues[r]=o.get())}removeValue(r){this.values.delete(r);const o=this.valueSubscriptions.get(r);o&&(o(),this.valueSubscriptions.delete(r)),delete this.latestValues[r],this.removeValueFromRenderState(r,this.renderState)}hasValue(r){return this.values.has(r)}getValue(r,o){if(this.props.values&&this.props.values[r])return this.props.values[r];let l=this.values.get(r);return l===void 0&&o!==void 0&&(l=Pi(o===null?void 0:o,{owner:this}),this.addValue(r,l)),l}readValue(r,o){var l;let u=this.latestValues[r]!==void 0||!this.current?this.latestValues[r]:(l=this.getBaseTargetFromProps(this.props,r))!==null&&l!==void 0?l:this.readValueFromInstance(this.current,r,this.options);return u!=null&&(typeof u=="string"&&(Qh(u)||Fh(u))?u=parseFloat(u):!Fw(u)&&kn.test(o)&&(u=Hh(r,o)),this.setBaseTarget(r,qe(u)?u.get():u)),qe(u)?u.get():u}setBaseTarget(r,o){this.baseTarget[r]=o}getBaseTarget(r){var o;const{initial:l}=this.props;let u;if(typeof l=="string"||typeof l=="object"){const f=Ya(this.props,l,(o=this.presenceContext)===null||o===void 0?void 0:o.custom);f&&(u=f[r])}if(l&&u!==void 0)return u;const d=this.getBaseTargetFromProps(this.props,r);return d!==void 0&&!qe(d)?d:this.initialValues[r]!==void 0&&u===void 0?void 0:this.baseTarget[r]}on(r,o){return this.events[r]||(this.events[r]=new uu),this.events[r].add(o)}notify(r,...o){this.events[r]&&this.events[r].notify(...o)}}class Am extends Bw{constructor(){super(...arguments),this.KeyframeResolver=qh}sortInstanceNodePosition(r,o){return r.compareDocumentPosition(o)&2?1:-1}getBaseTargetFromProps(r,o){return r.style?r.style[o]:void 0}removeValueFromRenderState(r,{vars:o,style:l}){delete o[r],delete l[r]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:r}=this.props;qe(r)&&(this.childSubscription=r.on("change",o=>{this.current&&(this.current.textContent=`${o}`)}))}}function Uw(n){return window.getComputedStyle(n)}class Ww extends Am{constructor(){super(...arguments),this.type="html",this.renderInstance=hh}readValueFromInstance(r,o){if(Un.has(o)){const l=hu(o);return l&&l.default||0}else{const l=Uw(r),u=(fh(o)?l.getPropertyValue(o):l[o])||0;return typeof u=="string"?u.trim():u}}measureInstanceViewportBox(r,{transformPagePoint:o}){return mm(r,o)}build(r,o,l){qa(r,o,l.transformTemplate)}scrapeMotionValuesFromProps(r,o,l){return nu(r,o,l)}}class $w extends Am{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Le}getBaseTargetFromProps(r,o){return r[o]}readValueFromInstance(r,o){if(Un.has(o)){const l=hu(o);return l&&l.default||0}return o=mh.has(o)?o:Ka(o),r.getAttribute(o)}scrapeMotionValuesFromProps(r,o,l){return vh(r,o,l)}build(r,o,l){Ja(r,o,this.isSVGTag,l.transformTemplate)}renderInstance(r,o,l,u){gh(r,o,l,u)}mount(r){this.isSVGTag=tu(r.tagName),super.mount(r)}}const Hw=(n,r)=>Qa(n)?new $w(r):new Ww(r,{allowProjection:n!==b.Fragment}),Kw=y0({...mx,...jw,...Pw,...Iw},Hw),Pt=Vv(Kw);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gw=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Mm=(...n)=>n.filter((r,o,l)=>!!r&&r.trim()!==""&&l.indexOf(r)===o).join(" ").trim();/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Qw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yw=b.forwardRef(({color:n="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:u="",children:d,iconNode:f,...p},m)=>b.createElement("svg",{ref:m,...Qw,width:r,height:r,stroke:n,strokeWidth:l?Number(o)*24/Number(r):o,className:Mm("lucide",u),...p},[...f.map(([g,y])=>b.createElement(g,y)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mt=(n,r)=>{const o=b.forwardRef(({className:l,...u},d)=>b.createElement(Yw,{ref:d,iconNode:r,className:Mm(`lucide-${Gw(n)}`,l),...u}));return o.displayName=`${n}`,o};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xw=mt("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zw=mt("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Up=mt("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qw=mt("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jw=mt("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eS=mt("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tS=mt("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nS=mt("Scale",[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rS=mt("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iS=mt("Swords",[["polyline",{points:"14.5 17.5 3 6 3 3 6 3 17.5 14.5",key:"1hfsw2"}],["line",{x1:"13",x2:"19",y1:"19",y2:"13",key:"1vrmhu"}],["line",{x1:"16",x2:"20",y1:"16",y2:"20",key:"1bron3"}],["line",{x1:"19",x2:"21",y1:"21",y2:"19",key:"13pww6"}],["polyline",{points:"14.5 6.5 18 3 21 3 21 6 17.5 9.5",key:"hbey2j"}],["line",{x1:"5",x2:"9",y1:"14",y2:"18",key:"1hf58s"}],["line",{x1:"7",x2:"4",y1:"17",y2:"20",key:"pidxm4"}],["line",{x1:"3",x2:"5",y1:"19",y2:"21",key:"1pehsh"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oS=mt("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sS=mt("Vote",[["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}],["path",{d:"M5 7c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v12H5V7Z",key:"1ezoue"}],["path",{d:"M22 19H2",key:"nuriw5"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lS=mt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function aS({title:n,onClick:r,index:o=0,icon:l}){const[u,d]=b.useState(!1),[f,p]=b.useState(""),m=n==="Define Your Own",g=()=>{m&&!u?d(!0):m||r()},y=()=>{f.trim()&&r(f)},x=()=>{d(!1),p("")},S=A=>{A.key==="Enter"?(A.preventDefault(),y()):A.key==="Escape"&&(A.preventDefault(),x())};return I.jsx(Pt.div,{initial:{scale:1,rotate:0},animate:{scale:[1,1.1,.95,1.05,1],rotate:[0,-2,2,-1,0]},transition:{duration:.6,delay:o*.1,ease:"easeInOut"},whileHover:{scale:u?1:1.05},children:I.jsx(ka,{className:`p-6 bg-card border-2 border-border transition-all duration-200 group ${u?"border-yellow bg-accent":"hover:bg-accent hover:border-yellow cursor-pointer"}`,onClick:u?void 0:g,children:I.jsx("div",{className:"flex flex-col items-center justify-center h-24",children:u?I.jsxs("div",{className:"w-full space-y-3",children:[I.jsx(Xp,{value:f,onChange:A=>p(A.target.value),onKeyDown:S,placeholder:"Enter your topic...",className:"text-center text-sm bg-background border-border focus:border-yellow",autoFocus:!0}),I.jsxs("div",{className:"flex justify-center gap-2",children:[I.jsx(Yo,{size:"sm",onClick:y,disabled:!f.trim(),className:"bg-yellow text-black hover:opacity-80 h-6 px-2",children:I.jsx(Zw,{className:"w-3 h-3"})}),I.jsx(Yo,{size:"sm",variant:"outline",onClick:x,className:"h-6 px-2 border-border hover:bg-accent",children:I.jsx(lS,{className:"w-3 h-3"})})]})]}):I.jsxs(I.Fragment,{children:[I.jsx(l,{className:"w-6 h-6 text-yellow-muted group-hover:text-yellow mb-3 transition-colors"}),I.jsx("span",{className:"text-foreground text-center group-hover:text-yellow transition-colors",children:n})]})})})})}const uS=qp("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function cS({className:n,variant:r,asChild:o=!1,...l}){const u=o?Zp:"span";return I.jsx(u,{"data-slot":"badge",className:rs(uS({variant:r}),n),...l})}function fS({roomName:n,onBack:r}){const[o,l]=b.useState(""),[u,d]=b.useState(300),[f,p]=b.useState(60),[m,g]=b.useState(!1),[y,x]=b.useState(!1),[S,A]=b.useState(!1),[N,M]=b.useState(!0),[R,_]=b.useState([]);b.useEffect(()=>{let W=null;return m&&u>0?W=setInterval(()=>{d(ne=>ne-1)},1e3):u===0&&g(!1),()=>{W&&clearInterval(W)}},[m,u]),b.useEffect(()=>{let W=null;return y&&f>0?W=setInterval(()=>{p(ne=>ne-1)},1e3):f===0&&y&&U(!0),()=>{W&&clearInterval(W)}},[y,f]);const F=W=>{const ne=Math.floor(W/60),X=W%60;return`${ne}:${X.toString().padStart(2,"0")}`},U=(W=!1)=>{if(o.trim()||W){const ne=o.trim()||"(no response)";_(X=>[...X,{text:ne,sender:"user"}]),S||(A(!0),g(!0)),x(!1),M(!1),l(""),setTimeout(()=>{_(X=>[...X,{text:`I disagree with "${ne}". Here's my counterargument...`,sender:"ai"}]),setTimeout(()=>{M(!0),p(60)},1e3)},2e3),console.log("Submitting argument:",ne,W?"(auto-submitted)":"")}},q=W=>{W.key==="Enter"&&!W.shiftKey&&(W.preventDefault(),U())},G=()=>{N&&!y&&x(!0)};return I.jsx("div",{className:"min-h-screen bg-background text-foreground",children:I.jsxs("div",{className:"container mx-auto px-4 py-8",children:[I.jsxs(Pt.div,{className:"flex items-center justify-between mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:[I.jsxs(Yo,{variant:"ghost",onClick:r,className:"flex items-center gap-2 text-muted-foreground hover:text-foreground",children:[I.jsx(Xw,{className:"w-4 h-4"}),"Back to Rooms"]}),I.jsxs(Pt.div,{className:"flex items-center gap-4",initial:{scale:.8},animate:{scale:1},transition:{duration:.5,delay:.2},children:[I.jsx(cS,{variant:"outline",className:"px-4 py-2 text-base border-2 border-yellow text-yellow",children:n}),I.jsxs("div",{className:"flex items-center gap-2 text-muted-foreground",children:[I.jsx(Up,{className:"w-4 h-4"}),I.jsx("span",{className:`text-lg font-mono ${m?"text-yellow":"text-muted-foreground"}`,children:F(u)}),I.jsx("span",{className:"text-sm text-muted-foreground",children:"session"})]})]})]}),I.jsx(Pt.div,{className:"max-w-4xl mx-auto mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},children:I.jsx(ka,{className:"p-6 bg-card border-2 border-border",children:I.jsxs("div",{className:"flex flex-col gap-4",children:[I.jsxs("div",{className:"flex gap-4",children:[I.jsx(Xp,{value:o,onChange:W=>l(W.target.value),onKeyPress:q,onFocus:G,placeholder:N?"Enter your argument statement":"Waiting for AI response...",className:"flex-1 bg-input border-2 border-border text-foreground placeholder:text-muted-foreground focus:border-yellow",disabled:!N}),I.jsx(Yo,{onClick:()=>U(),disabled:!N,className:"bg-yellow text-black hover:opacity-80 transition-opacity",children:I.jsx(rS,{className:"w-4 h-4"})})]}),!S&&I.jsx("p",{className:"text-sm text-muted-foreground text-center",children:"Press Enter or click send to start the debate"}),S&&!N&&I.jsx("p",{className:"text-sm text-muted-foreground text-center",children:"AI is responding..."})]})})}),y&&N&&I.jsx(Pt.div,{className:"max-w-4xl mx-auto mb-8",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},children:I.jsxs("div",{className:"text-center",children:[I.jsxs("div",{className:"inline-flex items-center gap-3 px-6 py-3 rounded-lg border-2 border-yellow bg-yellow/10",children:[I.jsx(Up,{className:"w-5 h-5 text-yellow"}),I.jsx("span",{className:"text-2xl font-mono text-yellow",children:F(f)}),I.jsx("span",{className:"text-sm text-muted-foreground",children:"per prompt"})]}),I.jsx("p",{className:"text-xs text-muted-foreground mt-2",children:"Timer auto-submits when it reaches 0:00"})]})}),I.jsx(Pt.div,{className:"max-w-4xl mx-auto",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.5},children:I.jsx(ka,{className:"min-h-[500px] p-6 bg-card border-2 border-border",children:S?I.jsxs("div",{className:"space-y-4 max-h-[400px] overflow-y-auto",children:[R.map((W,ne)=>I.jsx("div",{className:`flex ${W.sender==="user"?"justify-end":"justify-start"}`,children:I.jsxs("div",{className:`max-w-3xl rounded-lg p-4 ${W.sender==="user"?"bg-yellow/10 border-2 border-yellow/60":"bg-accent border-2 border-border"}`,children:[I.jsx("p",{className:"text-foreground",children:W.text}),I.jsx("span",{className:"text-xs text-muted-foreground mt-2 block",children:W.sender==="user"?"You":"Arguebot"})]})},ne)),!N&&I.jsx("div",{className:"flex justify-start",children:I.jsx("div",{className:"max-w-3xl bg-accent rounded-lg p-4",children:I.jsxs("div",{className:"flex items-center gap-2",children:[I.jsxs("div",{className:"flex space-x-1",children:[I.jsx("div",{className:"w-2 h-2 bg-yellow rounded-full animate-bounce"}),I.jsx("div",{className:"w-2 h-2 bg-yellow rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),I.jsx("div",{className:"w-2 h-2 bg-yellow rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),I.jsx("span",{className:"text-muted-foreground",children:"Arguebot is thinking..."})]})})})]}):I.jsx("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:I.jsxs("div",{className:"text-center",children:[I.jsx("p",{className:"text-lg mb-2",children:"Enter your argument to begin"}),I.jsx("p",{className:"text-sm",children:"The Arguebot is waiting for your challenge..."})]})})})}),S&&I.jsx(Pt.div,{className:"text-center mt-6",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.8},children:I.jsxs("p",{className:"text-sm text-muted-foreground",children:["Debate in progress • Session: ",m?"Active":"Paused"," • Turn: ",N?"Your turn":"AI turn"]})})]})})}const dS=[{name:"Law",icon:nS},{name:"Politics",icon:sS},{name:"Ethics",icon:eS},{name:"Cultural",icon:Jw},{name:"Technology",icon:qw},{name:"Define Your Own",icon:tS}];function pS(){const[n,r]=b.useState("rooms"),[o,l]=b.useState("");Ry.useEffect(()=>{document.documentElement.classList.add("dark")},[]);const u=(f,p)=>{const m=p||f;console.log(`Selected room: ${m}`),l(m),r("arena")},d=()=>{r("rooms"),l("")};return n==="arena"?I.jsx(fS,{roomName:o,onBack:d}):I.jsx("div",{className:"min-h-screen bg-background text-foreground",children:I.jsxs("div",{className:"container mx-auto px-4 py-12",children:[I.jsxs(Pt.div,{className:"text-center mb-16",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:[I.jsxs(Pt.div,{className:"flex items-center justify-center mb-6",initial:{scale:.8},animate:{scale:1},transition:{duration:.5,delay:.2},children:[I.jsx(iS,{className:"w-8 h-8 text-yellow-muted mr-3"}),I.jsx("h1",{className:"text-4xl md:text-5xl text-foreground",children:"Arguebot Arena"}),I.jsx(oS,{className:"w-8 h-8 text-yellow-muted ml-3"})]}),I.jsxs(Pt.div,{className:"space-y-4 max-w-2xl mx-auto",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.4},children:[I.jsxs("p",{className:"text-xl text-muted-foreground",children:["Try your luck against the ",I.jsx("span",{className:"text-yellow",children:"arguer"})]}),I.jsx("p",{className:"text-lg text-muted-foreground/80",children:"they haven't lost an argument since… ever"})]})]}),I.jsxs(Pt.div,{className:"max-w-4xl mx-auto",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.6},children:[I.jsxs("h2",{className:"text-2xl text-center mb-8 text-foreground",children:["Select a room below to enter the ",I.jsx("span",{className:"text-yellow",children:"arena"})," (no helmets provided)."]}),I.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:dS.map((f,p)=>I.jsx(aS,{title:f.name,icon:f.icon,index:p,onClick:m=>u(f.name,m)},p))})]}),I.jsx(Pt.div,{className:"text-center mt-16",initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:1.2},children:I.jsx("p",{className:"text-sm text-muted-foreground/60",children:"Choose your battlefield and prepare for intellectual combat"})})]})})}jy.createRoot(document.getElementById("root")).render(I.jsx(b.StrictMode,{children:I.jsx(pS,{})}));

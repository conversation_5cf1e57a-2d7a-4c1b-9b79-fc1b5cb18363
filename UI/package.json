{"name": "argubot-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "backend": "cd backend && python -m uvicorn app:app --reload --port 8000", "start": "concurrently \"npm run backend\" \"npm run dev\"", "setup-backend": "cd backend && pip install -r requirements.txt", "deploy": "npm run build && npm run deploy:netlify", "deploy:netlify": "npx netlify deploy --prod --dir=dist", "deploy:vercel": "npx vercel --prod", "deploy:surge": "npx surge dist/ argubot-ui.surge.sh"}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.11.17", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.17.0", "vite": "^6.3.5"}}